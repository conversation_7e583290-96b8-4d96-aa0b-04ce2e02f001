const { Events } = require('discord.js');
const { exec } = require('child_process');
const sticky = require('../commands/utility/sticky.js');
const { MessageFlags } = require('discord.js');
const ownerStatus = require('../commands/utility/owner-status.js');
const { logger } = require('../utils/consoleLogger.js');
const exp = require('../commands/utility/exp.js');
const { isInteractionInvalidated } = require('../utils/commandInvalidation.js');
const { optimizedUpdateOne } = require("../utils/database-optimizer.js");
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');
const { handleUIOperation } = require('../utils/interactionManager.js');
const { ContainerBuilder, TextDisplayBuilder } = require('discord.js');
const { OPERATION_COLORS } = require('../utils/colors.js');

/**
 * Interaction Create Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes interaction processing with comprehensive caching and performance monitoring
 * OPTIMIZED: LRU caching for command routing, interaction validation, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const interactionCreateMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    interactionsProcessed: 0,
    commandsExecuted: 0,
    buttonsHandled: 0,
    modalsHandled: 0,
    selectMenusHandled: 0,
    invalidatedInteractions: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: LRU caches for interaction processing
const commandCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for command data
const interactionValidationCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for validation results

// Register caches for global cleanup
registerCache(commandCache);
registerCache(interactionValidationCache);

/**
 * Get cached interaction invalidation status (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {Object} interaction - Discord interaction
 * @returns {Promise<boolean>} Whether interaction is invalidated
 */
async function getCachedInteractionValidation(interaction) {
    const startTime = Date.now();
    const cacheKey = `validation_${interaction.id}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = interactionValidationCache.get(cacheKey);
        if (cached !== undefined) {
            interactionCreateMetrics.cacheHits++;
            if (interactionCreateMetrics.verboseLogging) {
                console.log(`[interactionCreate] ⚡ Interaction validation cache hit for ${interaction.id} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        interactionCreateMetrics.cacheMisses++;
        interactionCreateMetrics.databaseQueries++;

        // Check if this interaction is from an invalidated command
        const isInvalidated = await isInteractionInvalidated(interaction);

        // Cache the result (shorter TTL for invalidation checks)
        interactionValidationCache.set(cacheKey, isInvalidated);

        const duration = Date.now() - startTime;
        interactionCreateMetrics.averageQueryTime =
            (interactionCreateMetrics.averageQueryTime * (interactionCreateMetrics.databaseQueries - 1) + duration) /
            interactionCreateMetrics.databaseQueries;

        if (interactionCreateMetrics.verboseLogging || duration > 50) {
            console.log(`[interactionCreate] ✅ Interaction validation checked for ${interaction.id}: ${duration}ms - cached for future access`);
        }

        return isInvalidated;
    } catch (error) {
        console.error('[interactionCreate] ❌ Error checking interaction validation:', error);
        return false; // Default to not invalidated on error
    }
}

/**
 * Get cached command data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for command routing
 * @param {string} commandName - Command name
 * @returns {Object|null} Cached command data
 */
function getCachedCommand(commandName) {
    const startTime = Date.now();
    const cacheKey = `command_${commandName}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = commandCache.get(cacheKey);
        if (cached) {
            interactionCreateMetrics.cacheHits++;
            if (interactionCreateMetrics.verboseLogging) {
                console.log(`[interactionCreate] ⚡ Command cache hit for ${commandName} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        interactionCreateMetrics.cacheMisses++;

        // This would be where we'd fetch command data if needed
        // For now, we'll cache the miss to avoid repeated lookups
        commandCache.set(cacheKey, null);

        const duration = Date.now() - startTime;
        if (interactionCreateMetrics.verboseLogging) {
            console.log(`[interactionCreate] ❌ Command cache miss for ${commandName}: ${duration}ms`);
        }

        return null;
    } catch (error) {
        console.error('[interactionCreate] ❌ Error getting cached command:', error);
        return null;
    }
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(client, interaction) {
        const startTime = Date.now();

        try {
            interactionCreateMetrics.interactionsProcessed++;

            // OPTIMIZED: Check cached interaction invalidation status
            const isInvalidated = await getCachedInteractionValidation(interaction);
            if (isInvalidated) {
                interactionCreateMetrics.invalidatedInteractions++;
                // Silently ignore interactions from invalidated commands
                // This prevents users from interacting with old command responses
                return;
            }

        if (interaction.isButton()) {
            interactionCreateMetrics.buttonsHandled++;
            if (interaction.customId === 'exp-create-level-exp') {
                console.log('[interactionCreate] Handling exp-create-level-exp');
                const result = await exp.buttons(interaction);
                console.log('[interactionCreate] exp-create-level-exp handled, returning');
                return result;
            }
            if (interaction.customId === 'owner-reload') {
                // CONVERTED: Use Universal Interaction Manager pattern for owner-reload
                await handleUIOperation(interaction, async (interaction) => {
                    // Save the current timestamp to the database (optimized to use shared connection)
                    const now = Math.floor(Date.now() / 1000);
                    await optimizedUpdateOne("stats",
                        { type: "last_reload" },
                        { $set: { timestamp: now } },
                        { upsert: true }
                    );
                    // Rebuild the owner container with the new timestamp
                    const owner = require('../commands/utility/owner.js');
                    const container = await owner.buildOwnerContainer(interaction.client, interaction.user);

                    // Schedule process exit after response
                    setTimeout(() => process.exit(0), 1000);

                    return [container];
                }, {
                    autoDefer: false, // Don't auto-defer for button presses - should be fast
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the reload command.'
                });
                return;
            }
            if (interaction.customId === 'owner-sync') {
                // CONVERTED: Use Universal Interaction Manager pattern for owner-sync
                await handleUIOperation(interaction, async (interaction) => {
                    // Save syncing state and timestamp to DB (optimized to use shared connection)
                    const now = Math.floor(Date.now() / 1000);
                    await optimizedUpdateOne("stats",
                        { type: "last_sync" },
                        { $set: { syncing: true, timestamp: now } },
                        { upsert: true }
                    );
                    // Rebuild the owner container with the new timestamp
                    const owner = require('../commands/utility/owner.js');
                    const container = await owner.buildOwnerContainer(interaction.client, interaction.user);

                    // Execute sync command in background
                    exec('node deploy-commands.js', { cwd: process.cwd() }, async () => {
                        // Just update the DB with syncing: false and new timestamp
                        const done = Math.floor(Date.now() / 1000);
                        await optimizedUpdateOne("stats",
                            { type: "last_sync" },
                            { $set: { syncing: false, timestamp: done } },
                            { upsert: true }
                        );
                        // No need to close shared connection
                    });

                    return [container];
                }, {
                    autoDefer: true, // Auto-defer for sync command as it may take time
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the sync command.'
                });
                return;
            }
            if (["invite", "info"].includes(interaction.customId)) {
                // CONVERTED: Use Universal Interaction Manager pattern for invite/info buttons
                await handleUIOperation(interaction, async (interaction) => {
                    if (interaction.customId == "invite") {
                        const container = new ContainerBuilder()
                            .addTextDisplayComponents(new TextDisplayBuilder()
                                .setContent('**status:** this isn\'t ever going to be used.'))
                            .setAccentColor(OPERATION_COLORS.NEUTRAL);
                        return [container];
                    }
                    else if (interaction.customId == "info") {
                        const container = new ContainerBuilder()
                            .addTextDisplayComponents(new TextDisplayBuilder()
                                .setContent('**info:** a bot with specialized features that others might find useful or interesting. to know more about a specific feature, click [here](https://19aliens.com/17).'))
                            .setAccentColor(OPERATION_COLORS.NEUTRAL);
                        return [container];
                    }
                    return [];
                }, {
                    autoDefer: false, // Don't auto-defer for simple info buttons
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing your request.'
                });
            }
            // Owner back button - explicit routing to ensure it works
            if (interaction.customId === 'owner-back') {
                const owner = require('../commands/utility/owner.js');
                return await owner.buttons(interaction, []);
            }
            // Sticky feature buttons
            if (['sticky-disable', 'sticky-enable'].includes(interaction.customId)) {
                return await sticky.buttons(interaction);
            }
            // Logs enable/disable button
            if (interaction.customId === 'logs-enabler') {
                const logs = require('../commands/utility/logs.js');
                await handleUIOperation(interaction, async (interaction) => {
                    return await logs.toggleEnabled(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error toggling the logs feature. Please try again.'
                });
                return;
            }

            // Opener enable/disable buttons
            if (['opener-disable', 'opener-enable'].includes(interaction.customId)) {
                const opener = require('../commands/utility/opener.js');
                return await opener.buttons(interaction);
            }
            // Items pagination buttons
            if (interaction.customId.startsWith('items-page-')) {
                const items = require('../commands/utility/items.js');
                return await items.buttons(interaction, []);
            }
            // Items creation back button
            if (interaction.customId === 'items-creation-back') {
                const items = require('../commands/utility/items.js');
                return await items.buttons(interaction, []);
            }
            // Items create final button
            if (interaction.customId === 'items-create-final') {
                const items = require('../commands/utility/items.js');
                return await items.buttons(interaction, []);
            }
            // Items toggle disable button
            if (interaction.customId === 'items-toggle-disable') {
                const items = require('../commands/utility/items.js');
                return await items.buttons(interaction, []);
            }

            // Items delete button
            if (interaction.customId === 'items-delete-item') {
                const items = require('../commands/utility/items.js');
                return await items.buttons(interaction, []);
            }
            // Items global enable/disable buttons
            if (interaction.customId === 'items-global-disable' || interaction.customId === 'items-global-enable') {
                const items = require('../commands/utility/items.js');
                return await items.buttons(interaction, []);
            }
            // You command notification dismiss buttons
            if (interaction.customId.startsWith('you-dismiss-notification-')) {
                const you = require('../commands/utility/you.js');
                return await you.buttons(interaction, []);
            }
            // Transcription process button
            if (interaction.customId === 'transcription-process') {
                const you = require('../commands/utility/you.js');
                return await you.buttons(interaction, []);
            }
            // Starfall claim buttons
            if (interaction.customId.startsWith('starfall-claim-')) {
                // CONVERTED: Use Universal Interaction Manager pattern for starfall buttons
                await handleUIOperation(interaction, async (interaction) => {
                    const { processStarfallClaim } = require('../utils/starfall.js');
                    return await processStarfallClaim(interaction);
                }, {
                    autoDefer: false, // Don't auto-defer for button presses - should be fast
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing your starfall claim. Please try again.'
                });
                return;
            }
            // Prestige system buttons
            if (interaction.customId.startsWith('prestige-confirm-') || interaction.customId.startsWith('prestige-cancel-')) {
                const { handlePrestigeClick, handlePrestigeCancel } = require('../utils/prestigeUI.js');
                if (interaction.customId.startsWith('prestige-confirm-')) {
                    await handlePrestigeClick(interaction);
                } else {
                    await handlePrestigeCancel(interaction);
                }
                return;
            }
            // Global level notification dismiss buttons
            if (interaction.customId.startsWith('dismiss-global-level-')) {
                // CONVERTED: Use Universal Interaction Manager pattern for global level dismissal
                await handleUIOperation(interaction, async (interaction) => {
                    const notificationId = interaction.customId.replace('dismiss-global-level-', '');
                    const { dismissGlobalLevelNotification } = require('../utils/globalLevelNotifications.js');

                    await dismissGlobalLevelNotification(notificationId);

                    // Return success container
                    const container = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder()
                            .setContent('**status:** ✅ Global level notification dismissed.'))
                        .setAccentColor(OPERATION_COLORS.SUCCESS);
                    return [container];
                }, {
                    autoDefer: false, // Don't auto-defer for dismiss buttons - should be fast
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error dismissing the notification.'
                });
                return;
            }
            // Global levels create/update buttons
            if (interaction.customId === 'global-level-create-final' || interaction.customId === 'global-level-update-final') {
                const owner = require('../commands/utility/owner.js');
                return await owner.buttons(interaction, []);
            }
            // Global levels back button
            if (interaction.customId === 'global-levels-back') {
                const owner = require('../commands/utility/owner.js');
                return await owner.buttons(interaction, []);
            }
            // Backup system buttons
            if (['owner-backup-main', 'backup-now', 'backup-stats', 'backup-back'].includes(interaction.customId)) {
                const owner = require('../commands/utility/owner.js');
                return await owner.buttons(interaction, []);
            }
            // Owner item notification back button (return to items interface)
            if (interaction.customId === 'owner-itemnotification-back') {
                // CONVERTED: Use Universal Interaction Manager pattern for owner item notification back
                await handleUIOperation(interaction, async (interaction) => {
                    const items = require('../commands/utility/items.js');
                    const container = await items.buildItemsContainer({
                        isOwner: true,
                        guildId: null, // Bot-wide items (not guild-specific)
                        page: 0,
                        client: interaction.client,
                        member: null, // No member context for bot-wide items
                        showBackButton: true // Show back button in owner panel context
                    });
                    return [container];
                }, {
                    autoDefer: false, // Don't auto-defer for back buttons - should be fast
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error returning to the items interface.'
                });
                return;
            }

            // EXP global enable/disable buttons
            if (interaction.customId === 'exp-global-disable' || interaction.customId === 'exp-global-enable') {
                return await exp.buttons(interaction);
            }
            // EXP voice enable/disable buttons
            if (interaction.customId === 'exp-voice-disable' || interaction.customId === 'exp-voice-enable') {
                return await exp.buttons(interaction);
            }
            // EXP text enable/disable buttons
            if (interaction.customId === 'exp-text-disable' || interaction.customId === 'exp-text-enable') {
                return await exp.buttons(interaction);
            }
            if (interaction.customId === 'exp-create-level-exp') {
                return await exp.buttons(interaction);
            }
            if (interaction.customId === 'exp-create-level-back') {
                await handleUIOperation(interaction, async (interaction) => {
                    return await exp.expCreateLevelBack(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the level creation. Please try again.'
                });
                return;
            }
            if (interaction.customId.startsWith('exp-edit-level-back-')) {
                await handleUIOperation(interaction, async (interaction) => {
                    return await exp.expEditLevelBack(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the level edit. Please try again.'
                });
                return;
            }
            if (interaction.customId === 'exp-levels-channel-back') {
                await handleUIOperation(interaction, async (interaction) => {
                    return await exp.expLevelChannelBack(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the channel configuration. Please try again.'
                });
                return;
            }
            // Unified level interface handlers
            if (interaction.customId === 'exp-create-level-final' || interaction.customId.startsWith('exp-modify-level-final-')) {
                return await exp.buttons(interaction);
            }
            if (interaction.customId === 'exp-unified-level-back' || interaction.customId.startsWith('exp-unified-edit-back-')) {
                return await exp.buttons(interaction);
            }

            else {
                const [command, ...args] = interaction.customId.split("-");
                console.log('[interactionCreate] Button interaction:', command, args);
                if (command === 'whisper') {
                    const whisperModels = require('../commands/utility/whisperModels.js');
                    return await whisperModels.buttons(interaction, args);
                }
                if (command === 'dehoist') {
                    const dehoist = require('../commands/utility/dehoist.js');
                    return await dehoist.buttons(interaction, args);
                }
                if (client.commands.get(command)?.buttons) {
                    return await client.commands.get(command)?.buttons(interaction, args);
                }
            }
        }

        else if (
            interaction.isStringSelectMenu() ||
            interaction.isRoleSelectMenu() ||
            interaction.isUserSelectMenu() ||
            interaction.isMentionableSelectMenu() ||
            interaction.isChannelSelectMenu()
        ) {
            interactionCreateMetrics.selectMenusHandled++;
            if (interaction.customId === 'exp-create-level-role') {
                console.log('[interactionCreate] Handling exp-create-level-role');
                const result = await exp.buttons(interaction);
                console.log('[interactionCreate] exp-create-level-role handled, returning');
                return result;
            }
            if (interaction.customId === 'exp-create-level-exp-select') {
                console.log('[interactionCreate] Handling exp-create-level-exp-select');
                await handleUIOperation(interaction, async (interaction) => {
                    return await exp.expCreateLevelExpSelect(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the EXP selection. Please try again.'
                });
                return;
            }
            if (interaction.customId === 'exp-create-level-back') {
                console.log('[interactionCreate] Handling exp-create-level-back');
                await exp.expCreateLevelBack(interaction);
                return;
            }
            if (interaction.customId.startsWith('exp-edit-level-exp-select-')) {
                console.log('[interactionCreate] Handling exp-edit-level-exp-select-*');
                await handleUIOperation(interaction, async (interaction) => {
                    return await exp.expEditLevelExpSelect(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the EXP edit selection. Please try again.'
                });
                return;
            }
            if (interaction.customId.startsWith('exp-edit-level-role-')) {
                console.log('[interactionCreate] Handling exp-edit-level-role-*');
                return await exp.buttons(interaction);
            }
            // Unified level interface select menus
            if (interaction.customId === 'exp-unified-level-role' || interaction.customId.startsWith('exp-unified-edit-role-')) {
                console.log('[interactionCreate] Handling unified level role select');
                return await exp.buttons(interaction);
            }
            if (interaction.customId === 'exp-unified-level-exp' || interaction.customId.startsWith('exp-unified-edit-exp-')) {
                console.log('[interactionCreate] Handling unified level exp select');
                return await exp.buttons(interaction);
            }
            if (interaction.customId === 'exp-unified-level-icon' || interaction.customId.startsWith('exp-unified-edit-icon-')) {
                console.log('[interactionCreate] Handling unified level icon select');
                return await exp.buttons(interaction);
            }
            if (interaction.customId === 'exp-levels-channel-select') {
                await handleUIOperation(interaction, async (interaction) => {
                    return await exp.select(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the channel selection. Please try again.'
                });
                return;
            }
            if (interaction.customId === 'exp-levels-channel-back') {
                await handleUIOperation(interaction, async (interaction) => {
                    return await exp.expLevelChannelBack(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the channel configuration. Please try again.'
                });
                return;
            }
            if (interaction.customId === 'items-notification-channel-select') {
                const command17 = require('../commands/utility/17.js');
                await command17.channelSelect(interaction);
                return;
            }
            console.log('[interactionCreate] SelectMenu triggered:', interaction.customId, interaction.values);
            const logsHandlers = require('../commands/utility/logs.js');
            try {
                if (interaction.customId === '17-thread-select') {
                    const opener = require('../commands/utility/opener.js');
                    return await opener.threadSelect(interaction);
                }
                if (interaction.customId.startsWith('logs-select')) {
                    console.log('[interactionCreate] About to call logs.selectModule');
                    return await logsHandlers.selectModule(interaction);
                } else if (interaction.customId.startsWith('logs-channel-select')) {
                    console.log('[interactionCreate] About to call logs.selectChannel');
                    return await logsHandlers.selectChannel(interaction);
                } else if (interaction.customId.startsWith('logs-core-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectCoreEvents');
                    return await logsHandlers.selectCoreEvents(interaction);
                } else if (interaction.customId.startsWith('logs-specialty-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectSpecialtyEvents');
                    return await logsHandlers.selectSpecialtyEvents(interaction);
                } else if (interaction.customId.startsWith('logs-owner-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectOwnerEvents');
                    return await logsHandlers.selectOwnerEvents(interaction);

                } else if (interaction.customId.startsWith('logs-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectEvents (legacy)');
                    console.log('[interactionCreate] logs.selectEvents is:', logsHandlers.selectEvents);
                    if (typeof logsHandlers.selectEvents === 'function') {
                        const result = await logsHandlers.selectEvents(interaction);
                        console.log('[interactionCreate] logs.selectEvents returned');
                        return result;
                    } else {
                        console.error('[interactionCreate] logs.selectEvents is not a function!');
                        logger.error('interactionCreate', 'logs.selectEvents is not a function!', interaction.client);
                    }
                } else if (interaction.customId.startsWith('status-')) {
                    // CONVERTED: Use Universal Interaction Manager pattern for status selects
                    await handleUIOperation(interaction, async (interaction) => {
                        // Only owner can access status feature
                        if (interaction.user.id !== process.env.OWNER) {
                            const errorContainer = new ContainerBuilder()
                                .addTextDisplayComponents(new TextDisplayBuilder()
                                    .setContent('**status:** ❌ Access denied. Owner only.'))
                                .setAccentColor(OPERATION_COLORS.DANGER);
                            return [errorContainer];
                        }

                        const container = await ownerStatus.handleStatusSelect(interaction);
                        return container ? [container] : []; // Return empty array if modal was shown
                    }, {
                        autoDefer: false, // Don't auto-defer for select menus - should be fast
                        ephemeral: true,
                        fallbackMessage: '❌ There was an error processing your status selection. Please try again.'
                    });
                    return;
                } else {
                    // Existing sticky and other feature routing
                    if (['sticky-nick-select', 'sticky-role-select'].includes(interaction.customId)) {
                        await handleUIOperation(interaction, async (interaction) => {
                            return await sticky.select(interaction);
                        }, {
                            autoDefer: false,
                            ephemeral: true,
                            fallbackMessage: '❌ There was an error processing the sticky selection. Please try again.'
                        });
                        return;
                    }
                    if (interaction.customId === 'exp-subcomponent-select') {
                        await handleUIOperation(interaction, async (interaction) => {
                            return await exp.select(interaction);
                        }, {
                            autoDefer: false,
                            ephemeral: true,
                            fallbackMessage: '❌ There was an error processing the EXP subcomponent selection. Please try again.'
                        });
                        return;
                    }
                    if (interaction.customId === 'exp-levels-config') {
                        await handleUIOperation(interaction, async (interaction) => {
                            return await exp.select(interaction);
                        }, {
                            autoDefer: false,
                            ephemeral: true,
                            fallbackMessage: '❌ There was an error processing the levels configuration. Please try again.'
                        });
                        return;
                    }
                    if (interaction.customId === 'exp-text-config') {
                        await handleUIOperation(interaction, async (interaction) => {
                            return await exp.select(interaction);
                        }, {
                            autoDefer: false,
                            ephemeral: true,
                            fallbackMessage: '❌ There was an error processing the text EXP configuration. Please try again.'
                        });
                        return;
                    }
                    if (interaction.customId === 'exp-voice-config') {
                        await handleUIOperation(interaction, async (interaction) => {
                            return await exp.select(interaction);
                        }, {
                            autoDefer: false,
                            ephemeral: true,
                            fallbackMessage: '❌ There was an error processing the voice EXP configuration. Please try again.'
                        });
                        return;
                    }
                    const [command, ...args] = interaction.customId.split("-");
                    if (interaction.customId === 'owner-features') {
                        const owner = require('../commands/utility/owner.js');
                        return await owner.select(interaction, args);
                    }
                    if (interaction.customId === 'changelog-select') {
                        const changelog = require('../commands/utility/changelog.js');
                        return await changelog.select(interaction, args);
                    }
                    if (interaction.customId === 'owner-joinnotification') {
                        const owner = require('../commands/utility/owner.js');
                        return await owner.joinNotificationSelect(interaction);
                    }
                    if (interaction.customId === 'owner-itemnotification') {
                        const owner = require('../commands/utility/owner.js');
                        await owner.itemNotificationSelect(interaction);
                        return;
                    }
                    // Backup system select menus
                    if (interaction.customId === 'backup-config') {
                        const owner = require('../commands/utility/owner.js');
                        return await owner.backupConfigSelect(interaction);
                    }
                    if (interaction.customId === 'backup-frequency') {
                        const owner = require('../commands/utility/owner.js');
                        return await owner.backupFrequencySelect(interaction);
                    }
                    if (interaction.customId === 'backup-channel') {
                        const owner = require('../commands/utility/owner.js');
                        return await owner.backupChannelSelect(interaction);
                    }
                    if (interaction.customId === 'clear-data-action') {
                        const clearData = require('../commands/utility/clearData.js');
                        await handleUIOperation(interaction, async (interaction) => {
                            return await clearData.select(interaction);
                        }, {
                            autoDefer: false,
                            ephemeral: true,
                            fallbackMessage: '❌ There was an error processing the data clearing action. Please try again.'
                        });
                        return;
                    }
                    if (interaction.customId === 'whisper-model-select') {
                        const whisperModels = require('../commands/utility/whisperModels.js');
                        await handleUIOperation(interaction, async (interaction) => {
                            return await whisperModels.select(interaction);
                        }, {
                            autoDefer: false,
                            ephemeral: true,
                            fallbackMessage: '❌ There was an error processing the Whisper model selection. Please try again.'
                        });
                        return;
                    }
                    if (interaction.customId === 'you-hub-select' || interaction.customId === 'you-feature-select' || interaction.customId === 'you-settings-select' || interaction.customId === 'you-inventory-item-select' || interaction.customId === 'you-inventory-type-select' || interaction.customId === 'transcription-voice-select') {
                        const you = require('../commands/utility/you.js');
                        return await you.select(interaction, args);
                    }
                    // Items select menus
                    if (['items-config-select', 'items-type-select', 'items-rarity-select', 'items-image-select', 'items-drop-locations-select'].includes(interaction.customId) ||
                        interaction.customId.startsWith('items-select-') || // Handle items-select-owner and items-select-guild
                        interaction.customId.startsWith('items-param-select-')) {
                        const items = require('../commands/utility/items.js');
                        return await items.select(interaction, args);
                    }
                    // Global levels select menus
                    if (interaction.customId === 'global-levels-action' ||
                        interaction.customId === 'global-level-config-select' ||
                        interaction.customId === 'global-level-exp-select' ||
                        interaction.customId === 'global-level-icon-select' ||
                        interaction.customId === 'global-prestige-icon-select' ||
                        interaction.customId === 'global-level-edit-config-select' ||
                        interaction.customId === 'global-level-edit-exp-select' ||
                        interaction.customId === 'global-level-edit-icon-select' ||
                        interaction.customId === 'global-level-edit-prestige-icon-select' ||
                        // Reward select menus
                        interaction.customId === 'global-level-items-select' ||
                        interaction.customId === 'global-level-edit-items-select' ||
                        interaction.customId === 'global-level-xp-booster-select' ||
                        interaction.customId === 'global-level-edit-xp-booster-select' ||
                        interaction.customId === 'global-level-drop-booster-select' ||
                        interaction.customId === 'global-level-edit-drop-booster-select' ||
                        interaction.customId === 'global-level-stars-select' ||
                        interaction.customId === 'global-level-edit-stars-select') {
                        const owner = require('../commands/utility/owner.js');
                        return await owner.select(interaction, args);
                    }

                    if (client.commands.get(command)?.select) {
                        return await client.commands.get(command)?.select(interaction, args);
                    }
                }
            } catch (err) {
                console.error('[interactionCreate] Error handling select menu:', err);
                logger.error('interactionCreate', `Error handling select menu ${interaction.customId}: ${err.message}`, interaction.client);

                // CONVERTED: Use simple error handling since this is outside Universal Interaction Manager scope
                // This is a fallback for select menu errors not handled by Universal Interaction Manager
                if (!interaction.replied && !interaction.deferred) {
                    try {
                        await interaction.reply({
                            content: '❌ An error occurred while processing your selection. Please try again.',
                            ephemeral: true
                        });
                    } catch (replyError) {
                        // Silently fail - interaction might be expired
                        console.warn('[interactionCreate] Failed to send error reply:', replyError.message);
                    }
                }
            }
        }
        else if (interaction.isModalSubmit()) {
            interactionCreateMetrics.modalsHandled++;
            if (interaction.customId === 'owner-joinnotification-modal') {
                const owner = require('../commands/utility/owner.js');
                await handleUIOperation(interaction, async (interaction) => {
                    return await owner.joinNotificationModal(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the join notification settings. Please try again.'
                });
                return;
            }
            if (interaction.customId === 'owner-itemnotification-modal') {
                const owner = require('../commands/utility/owner.js');
                await handleUIOperation(interaction, async (interaction) => {
                    return await owner.itemNotificationModal(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the item notification settings. Please try again.'
                });
                return;
            }
            // Handle items modals
            if (interaction.customId.startsWith('items-') && interaction.customId.includes('-modal')) {
                // Check if it's a DM message template modal (handled by 17.js)
                if (interaction.customId === 'items-dm-message-template-modal') {
                    const command17 = require('../commands/utility/17.js');
                    return await command17.modalSubmit(interaction);
                }
                // Other items modals handled by items.js
                const items = require('../commands/utility/items.js');
                return await items.modalSubmit(interaction);
            }
            // Handle status modals FIRST (before exp modals)
            if (interaction.customId.startsWith('status-msg-modal') || interaction.customId.startsWith('owner-status-msg-modal')) {
                // CONVERTED: Use Universal Interaction Manager pattern for status modal
                await handleUIOperation(interaction, async (interaction) => {
                    console.log('[interactionCreate] Status modal submission detected:', interaction.customId);
                    // Only owner can access status feature
                    if (interaction.user.id !== process.env.OWNER) {
                        console.log('[interactionCreate] Non-owner tried to access status modal');
                        const errorContainer = new ContainerBuilder()
                            .addTextDisplayComponents(new TextDisplayBuilder()
                                .setContent('**status:** ❌ Access denied. Owner only.'))
                            .setAccentColor(OPERATION_COLORS.DANGER);
                        return [errorContainer];
                    }

                    console.log('[interactionCreate] Owner verified, processing modal');
                    const container = await ownerStatus.handleStatusModal(interaction);
                    console.log('[interactionCreate] Container received, returning components');
                    return [container];
                }, {
                    autoDefer: false, // Don't auto-defer for modal submissions - should be fast
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the status message.'
                });
                return;
            }

            // Handle dehoist modals
            if (interaction.customId === 'dehoist-editchars-modal') {
                const dehoist = require('../commands/utility/dehoist.js');
                await handleUIOperation(interaction, async (interaction) => {
                    return await dehoist.modal(interaction);
                }, {
                    autoDefer: false,
                    ephemeral: true,
                    fallbackMessage: '❌ There was an error processing the dehoist character settings. Please try again.'
                });
                return;
            }

            // Handle global levels modals
            if (interaction.customId.startsWith('global-level-')) {
                const owner = require('../commands/utility/owner.js');
                return await owner.modalSubmit(interaction);
            }

            // Handle EXP modals (fallback for any remaining modals)
            return await exp.modalSubmit(interaction);

        }
        else if (interaction.isChannelSelectMenu()) {
            console.log('[interactionCreate] Channel select menu triggered:', interaction.customId);
            const [command, ...args] = interaction.customId.split("-");
            if (client.commands.get(command)?.threadSelect) {
                return await client.commands.get(command).threadSelect(interaction, args);
            }
        }
        // Handle both slash commands and context menu commands
        if (!interaction.isChatInputCommand() && !interaction.isContextMenuCommand()) return;

        interactionCreateMetrics.commandsExecuted++;

        const command = interaction.client.commands.get(interaction.commandName);

        if (!command) {
            console.error(`No command matching ${interaction.commandName} was found.`);
            return;
        }

        try {
            await command.execute(interaction);
        } catch (error) {
            console.error(`[interactionCreate] Command ${interaction.commandName} failed:`, error);
            // FIXED: Don't try to respond here - let commands handle their own errors
            // This was causing double acknowledgment issues
        }

        } catch (error) {
            console.error('[interactionCreate] ❌ Error processing interaction:', error);
        } finally {
            // Track performance metrics
            const duration = Date.now() - startTime;
            if (interactionCreateMetrics.verboseLogging || duration > 100) {
                console.log(`[interactionCreate] ✅ Interaction processed in ${duration}ms (type: ${interaction.type})`);
            }
        }
    },

    // Enhanced optimization functions
    getCachedInteractionValidation,
    getCachedCommand,
    getInteractionCreateStats,
    performanceCleanupAndOptimization,
    clearAllInteractionCreateCaches
};

/**
 * Get comprehensive interaction create performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive interaction create performance data
 */
function getInteractionCreateStats() {
    const cacheHitRate = interactionCreateMetrics.cacheHits + interactionCreateMetrics.cacheMisses > 0 ?
        (interactionCreateMetrics.cacheHits / (interactionCreateMetrics.cacheHits + interactionCreateMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: interactionCreateMetrics.cacheHits,
            cacheMisses: interactionCreateMetrics.cacheMisses,
            databaseQueries: interactionCreateMetrics.databaseQueries,
            averageQueryTime: `${interactionCreateMetrics.averageQueryTime.toFixed(2)}ms`,
            interactionsProcessed: interactionCreateMetrics.interactionsProcessed,
            commandsExecuted: interactionCreateMetrics.commandsExecuted,
            buttonsHandled: interactionCreateMetrics.buttonsHandled,
            modalsHandled: interactionCreateMetrics.modalsHandled,
            selectMenusHandled: interactionCreateMetrics.selectMenusHandled,
            invalidatedInteractions: interactionCreateMetrics.invalidatedInteractions,
            lastOptimization: new Date(interactionCreateMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            command: commandCache.getStats(),
            interactionValidation: interactionValidationCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            command: commandCache.getStats().memoryUsage,
            interactionValidation: interactionValidationCache.getStats().memoryUsage,
            total: commandCache.getStats().memoryUsage + interactionValidationCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization',
            invalidationRate: interactionCreateMetrics.interactionsProcessed > 0 ?
                `${(interactionCreateMetrics.invalidatedInteractions / interactionCreateMetrics.interactionsProcessed * 100).toFixed(2)}%` : '0%'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    interactionCreateMetrics.lastOptimization = Date.now();

    const stats = getInteractionCreateStats();
    if (interactionCreateMetrics.verboseLogging) {
        console.log(`[interactionCreate] 📊 Performance Report:`);
        console.log(`[interactionCreate]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[interactionCreate]   Interactions Processed: ${stats.performance.interactionsProcessed}`);
        console.log(`[interactionCreate]   Commands Executed: ${stats.performance.commandsExecuted}`);
        console.log(`[interactionCreate]   Buttons Handled: ${stats.performance.buttonsHandled}`);
        console.log(`[interactionCreate]   Modals Handled: ${stats.performance.modalsHandled}`);
        console.log(`[interactionCreate]   Select Menus Handled: ${stats.performance.selectMenusHandled}`);
        console.log(`[interactionCreate]   Invalidated Interactions: ${stats.performance.invalidatedInteractions}`);
        console.log(`[interactionCreate]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[interactionCreate]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[interactionCreate]   System Health: ${stats.systemHealth.status}`);
        console.log(`[interactionCreate]   Invalidation Rate: ${stats.systemHealth.invalidationRate}`);
    }

    return stats;
}

/**
 * Clear all interaction create caches (Enterprise-Grade Cache Management)
 */
function clearAllInteractionCreateCaches() {
    commandCache.clear();
    interactionValidationCache.clear();

    console.log('[interactionCreate] 🗑️ Cleared all interaction create caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, interactionCreateMetrics.performanceReportInterval);