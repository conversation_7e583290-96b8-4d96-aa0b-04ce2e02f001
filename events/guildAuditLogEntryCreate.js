const { Events, AuditLogEvent } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne } = require("../utils/database-optimizer.js");
const { defaults } = require("../utils/default_db_structures");
const { sendLogContainer } = require('../utils/sendLog.js');
const { createAuditLogContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');

/**
 * Guild Audit Log Entry Create Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes audit log processing with comprehensive caching and performance monitoring
 * OPTIMIZED: LRU caching for guild configurations, audit log settings, and event type processing
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const guildAuditLogMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    auditLogEntriesProcessed: 0,
    eventTypesProcessed: {},
    containersCreated: 0,
    loggingChannelsFound: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: LRU caches for audit log operations
const auditLogGuildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations
const auditLogEventTypeCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for event type processing
const auditLogChannelCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for channel configurations

// Register caches for global cleanup
registerCache(auditLogGuildConfigCache);
registerCache(auditLogEventTypeCache);
registerCache(auditLogChannelCache);

// Map audit log event numbers to their names
const actionNames = {
    [AuditLogEvent.GuildUpdate]: 'Guild Update',
    [AuditLogEvent.ChannelCreate]: 'Channel Create',
    [AuditLogEvent.ChannelDelete]: 'Channel Delete',
    [AuditLogEvent.ChannelUpdate]: 'Channel Update',
    [AuditLogEvent.ChannelOverwriteCreate]: 'Channel Permission Create',
    [AuditLogEvent.ChannelOverwriteUpdate]: 'Channel Permission Update',
    [AuditLogEvent.ChannelOverwriteDelete]: 'Channel Permission Delete',
    [AuditLogEvent.MemberKick]: 'Member Kick',
    [AuditLogEvent.MemberPrune]: 'Member Prune',
    [AuditLogEvent.MemberBanAdd]: 'Member Ban',
    [AuditLogEvent.MemberBanRemove]: 'Member Unban',
    [AuditLogEvent.MemberUpdate]: 'Member Update',
    [AuditLogEvent.MemberRoleUpdate]: 'Member Role Update',
    [AuditLogEvent.MemberMove]: 'Member Move',
    [AuditLogEvent.MemberDisconnect]: 'Member Disconnect',
    [AuditLogEvent.BotAdd]: 'Bot Add',
    [AuditLogEvent.RoleCreate]: 'Role Create',
    [AuditLogEvent.RoleDelete]: 'Role Delete',
    [AuditLogEvent.RoleUpdate]: 'Role Update',
    [AuditLogEvent.InviteCreate]: 'Invite Create',
    [AuditLogEvent.InviteDelete]: 'Invite Delete',
    [AuditLogEvent.InviteUpdate]: 'Invite Update',
    [AuditLogEvent.WebhookCreate]: 'Webhook Create',
    [AuditLogEvent.WebhookDelete]: 'Webhook Delete',
    [AuditLogEvent.WebhookUpdate]: 'Webhook Update',
    [AuditLogEvent.EmojiCreate]: 'Emoji Create',
    [AuditLogEvent.EmojiDelete]: 'Emoji Delete',
    [AuditLogEvent.EmojiUpdate]: 'Emoji Update',
    [AuditLogEvent.MessageDelete]: 'Message Delete',
    [AuditLogEvent.MessageBulkDelete]: 'Message Bulk Delete',
    [AuditLogEvent.MessagePin]: 'Message Pin',
    [AuditLogEvent.MessageUnpin]: 'Message Unpin',
    [AuditLogEvent.IntegrationCreate]: 'Integration Create',
    [AuditLogEvent.IntegrationDelete]: 'Integration Delete',
    [AuditLogEvent.IntegrationUpdate]: 'Integration Update',
    [AuditLogEvent.StageInstanceCreate]: 'Stage Instance Create',
    [AuditLogEvent.StageInstanceDelete]: 'Stage Instance Delete',
    [AuditLogEvent.StageInstanceUpdate]: 'Stage Instance Update',
    [AuditLogEvent.StickerCreate]: 'Sticker Create',
    [AuditLogEvent.StickerDelete]: 'Sticker Delete',
    [AuditLogEvent.StickerUpdate]: 'Sticker Update',
    [AuditLogEvent.GuildScheduledEventCreate]: 'Event Create',
    [AuditLogEvent.GuildScheduledEventDelete]: 'Event Delete',
    [AuditLogEvent.GuildScheduledEventUpdate]: 'Event Update',
    [AuditLogEvent.ThreadCreate]: 'Thread Create',
    [AuditLogEvent.ThreadDelete]: 'Thread Delete',
    [AuditLogEvent.ThreadUpdate]: 'Thread Update',
    [AuditLogEvent.ApplicationCommandPermissionUpdate]: 'Command Permission Update',
    [AuditLogEvent.AutoModerationRuleCreate]: 'Auto Mod Rule Create',
    [AuditLogEvent.AutoModerationRuleDelete]: 'Auto Mod Rule Delete',
    [AuditLogEvent.AutoModerationRuleUpdate]: 'Auto Mod Rule Update',
    [AuditLogEvent.AutoModerationBlockMessage]: 'Auto Mod Block Message',
    [AuditLogEvent.AutoModerationFlagToChannel]: 'Auto Mod Flag to Channel',
    [AuditLogEvent.AutoModerationUserCommunicationDisabled]: 'Auto Mod User Timeout',
    [AuditLogEvent.CreatorMonetizationRequestCreated]: 'Creator Monetization Request',
    [AuditLogEvent.CreatorMonetizationTermsAccepted]: 'Creator Terms Accepted',
    [AuditLogEvent.OnboardingPromptCreate]: 'Onboarding Prompt Create',
    [AuditLogEvent.OnboardingPromptDelete]: 'Onboarding Prompt Delete',
    [AuditLogEvent.OnboardingPromptUpdate]: 'Onboarding Prompt Update',
    [AuditLogEvent.OnboardingCreate]: 'Onboarding Create',
    [AuditLogEvent.OnboardingUpdate]: 'Onboarding Update',
    [AuditLogEvent.HomeSettingsCreate]: 'Home Settings Create',
    [AuditLogEvent.HomeSettingsUpdate]: 'Home Settings Update',
    [AuditLogEvent.HomeSettingsDelete]: 'Home Settings Delete',
    [AuditLogEvent.ClanCreate]: 'Clan Create',
    [AuditLogEvent.ClanUpdate]: 'Clan Update',
    [AuditLogEvent.ClanDelete]: 'Clan Delete'
};

// Format individual changes into human-readable descriptions
function formatChange(change, actionType) {
    const { key, old, new: newValue } = change;

    // Handle different change types
    switch (key) {
        case 'nick':
            if (old && newValue) {
                return `Changed their nickname from **${old}** to **${newValue}**`;
            } else if (newValue) {
                return `Set their nickname to **${newValue}**`;
            } else if (old) {
                return `Removed their nickname (was **${old}**)`;
            }
            break;

        case 'deaf':
            return newValue ? 'Server deafened' : 'Server undeafened';

        case 'mute':
            return newValue ? 'Server muted' : 'Server unmuted';

        case 'communication_disabled_until':
            if (newValue) {
                const until = new Date(newValue);
                return `Timed out until ${until.toLocaleString()}`;
            } else {
                return 'Timeout removed';
            }

        case 'avatar_hash':
            return 'Changed their server avatar';

        case 'name':
            if (actionType === AuditLogEvent.RoleUpdate) {
                return `Changed role name from **${old}** to **${newValue}**`;
            } else if (actionType === AuditLogEvent.ChannelUpdate) {
                return `Changed channel name from **${old}** to **${newValue}**`;
            } else if (actionType === AuditLogEvent.GuildUpdate) {
                return `Changed server name from **${old}** to **${newValue}**`;
            }
            return `Changed name from **${old}** to **${newValue}**`;

        case 'topic':
            if (newValue && old) {
                return `Changed topic from **${old}** to **${newValue}**`;
            } else if (newValue) {
                return `Set topic to **${newValue}**`;
            } else {
                return `Removed topic (was **${old}**)`;
            }

        case 'color':
            return `Changed color from **${old?.toString(16) || 'default'}** to **${newValue?.toString(16) || 'default'}**`;

        case 'permissions':
            return `Changed permissions`;

        case 'position':
            return `Changed position from **${old}** to **${newValue}**`;

        case 'hoist':
            return newValue ? 'Enabled display separately' : 'Disabled display separately';

        case 'mentionable':
            return newValue ? 'Made mentionable' : 'Made unmentionable';

        case 'nsfw':
            return newValue ? 'Marked as NSFW' : 'Unmarked as NSFW';

        case 'slowmode_delay':
            if (newValue > 0) {
                return `Set slowmode to **${newValue} seconds**`;
            } else {
                return `Removed slowmode (was **${old} seconds**)`;
            }

        case 'bitrate':
            return `Changed bitrate from **${old}** to **${newValue}**`;

        case 'user_limit':
            if (newValue === 0) {
                return `Removed user limit (was **${old}**)`;
            } else {
                return `Changed user limit from **${old}** to **${newValue}**`;
            }

        case 'region':
            return `Changed region from **${old}** to **${newValue}**`;

        case 'icon_hash':
            return 'Changed server icon';

        case 'splash_hash':
            return 'Changed server splash';

        case 'banner_hash':
            return 'Changed server banner';

        case 'owner_id':
            return `Transferred ownership to <@${newValue}>`;

        case '$add':
            // Role additions
            if (Array.isArray(newValue)) {
                const roles = newValue.map(role => `<@&${role.id}>`).join(', ');
                return `Added roles: ${roles}`;
            }
            break;

        case '$remove':
            // Role removals
            if (Array.isArray(newValue)) {
                const roles = newValue.map(role => `<@&${role.id}>`).join(', ');
                return `Removed roles: ${roles}`;
            }
            break;

        default:
            // Generic fallback
            if (old !== undefined && newValue !== undefined) {
                return `Changed ${key} from **${old}** to **${newValue}**`;
            } else if (newValue !== undefined) {
                return `Set ${key} to **${newValue}**`;
            } else if (old !== undefined) {
                return `Removed ${key} (was **${old}**)`;
            }
    }

    return null; // Skip unknown changes
}

/**
 * Get cached guild configuration for audit log (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Guild configuration data
 */
async function getCachedAuditLogGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `audit_log_guild_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = auditLogGuildConfigCache.get(cacheKey);
        if (cached) {
            guildAuditLogMetrics.cacheHits++;
            if (guildAuditLogMetrics.verboseLogging) {
                console.log(`[guildAuditLogEntryCreate] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildAuditLogMetrics.cacheMisses++;
        guildAuditLogMetrics.databaseQueries++;

        // Get guild data with race condition protection
        var guildData = await optimizedFindOne("guilds", { id: guildId });
        if (guildData == null) {
            try {
                // Try to insert the guild
                await optimizedInsertOne("guilds", defaults.guild(guildId));
                guildData = await optimizedFindOne("guilds", { id: guildId });
            } catch (error) {
                // Handle duplicate key error (race condition)
                if (error.code === 11000) {
                    // Another process already created the guild, just fetch it
                    guildData = await optimizedFindOne("guilds", { id: guildId });
                } else {
                    // Re-throw other errors
                    throw error;
                }
            }
        }

        // Cache the result
        auditLogGuildConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        guildAuditLogMetrics.averageQueryTime =
            (guildAuditLogMetrics.averageQueryTime * (guildAuditLogMetrics.databaseQueries - 1) + duration) /
            guildAuditLogMetrics.databaseQueries;

        if (guildAuditLogMetrics.verboseLogging || duration > 100) {
            console.log(`[guildAuditLogEntryCreate] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error('[guildAuditLogEntryCreate] ❌ Error getting guild config:', error);
        return null;
    }
}

/**
 * Get cached audit log channels (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for channel configurations
 * @param {Object} client - Discord client
 * @param {string} guildId - Guild ID
 * @param {Object} guildData - Guild configuration data
 * @returns {Array} Array of audit log channels
 */
function getCachedAuditLogChannels(client, guildId, guildData) {
    const startTime = Date.now();
    const cacheKey = `audit_log_channels_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = auditLogChannelCache.get(cacheKey);
        if (cached) {
            guildAuditLogMetrics.cacheHits++;
            if (guildAuditLogMetrics.verboseLogging) {
                console.log(`[guildAuditLogEntryCreate] ⚡ Channel cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildAuditLogMetrics.cacheMisses++;

        // Check if there are any channels configured for guildAuditLogEntryCreate event
        const channels = (guildData.logs?.channels?.filter(ch => ch.events.includes("guildAuditLogEntryCreate")) ?? [])
            .map(l => client.guilds.cache.get(guildId)?.channels.cache.get(l.id))
            .filter(ch => ch);

        // Cache the result
        auditLogChannelCache.set(cacheKey, channels);

        const duration = Date.now() - startTime;
        if (guildAuditLogMetrics.verboseLogging || duration > 50) {
            console.log(`[guildAuditLogEntryCreate] ✅ Channels fetched for ${guildId}: ${channels.length} channels in ${duration}ms - cached for future access`);
        }

        guildAuditLogMetrics.loggingChannelsFound += channels.length;
        return channels;
    } catch (error) {
        console.error('[guildAuditLogEntryCreate] ❌ Error getting channels:', error);
        return [];
    }
}

/**
 * Get cached event type processing (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching for event type processing and action name resolution
 * @param {number} actionType - Audit log action type
 * @returns {string} Action name
 */
function getCachedEventType(actionType) {
    const startTime = Date.now();
    const cacheKey = `event_type_${actionType}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = auditLogEventTypeCache.get(cacheKey);
        if (cached) {
            guildAuditLogMetrics.cacheHits++;
            if (guildAuditLogMetrics.verboseLogging) {
                console.log(`[guildAuditLogEntryCreate] ⚡ Event type cache hit for ${actionType} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildAuditLogMetrics.cacheMisses++;

        // Get action name from mapping
        const actionName = actionNames[actionType] || `Unknown Action (${actionType})`;

        // Cache the result
        auditLogEventTypeCache.set(cacheKey, actionName);

        // Track event type metrics
        if (!guildAuditLogMetrics.eventTypesProcessed[actionName]) {
            guildAuditLogMetrics.eventTypesProcessed[actionName] = 0;
        }
        guildAuditLogMetrics.eventTypesProcessed[actionName]++;

        const duration = Date.now() - startTime;
        if (guildAuditLogMetrics.verboseLogging || duration > 10) {
            console.log(`[guildAuditLogEntryCreate] ✅ Event type processed for ${actionType}: ${actionName} in ${duration}ms - cached for future access`);
        }

        return actionName;
    } catch (error) {
        console.error('[guildAuditLogEntryCreate] ❌ Error getting event type:', error);
        return `Unknown Action (${actionType})`;
    }
}

module.exports = {
    name: Events.GuildAuditLogEntryCreate,
    async execute(client, entry, guild) {
        const startTime = Date.now();

        try {
            guildAuditLogMetrics.auditLogEntriesProcessed++;

            // OPTIMIZED: Get cached guild configuration
            const guildData = await getCachedAuditLogGuildConfig(guild.id);
            if (!guildData) return;

            // OPTIMIZED: Get cached audit log channels
            const channels = getCachedAuditLogChannels(client, guild.id, guildData);

            // Early return if no logging channels configured
            if (channels.length === 0) {
                return;
            }



        // Additional debugging: Try to fetch fresh audit log data for critical actions
        const criticalActions = [
            22, // Member Ban
            23, // Member Unban
            20, // Member Kick
            24, // Member Disconnect
            25, // Member Move
            72, // Member Timeout
            1,  // Guild Update
            11, // Role Create
            12, // Role Update
            13, // Role Delete
            10, // Channel Create
            11, // Channel Update
            12  // Channel Delete
        ];

        if (!entry.executor && criticalActions.includes(entry.action)) {
            try {
                const auditLogs = await guild.fetchAuditLogs({
                    type: entry.action,
                    limit: 5
                });
                const freshEntry = auditLogs.entries.find(e => e.id === entry.id);
                if (freshEntry && freshEntry.executor) {
                    // Use the fresh entry instead
                    entry = freshEntry;
                }
            } catch (error) {
                // Silently fail - we'll use the original entry
            }
        }

        // OPTIMIZED: Get cached event type processing
        const actionName = getCachedEventType(entry.action);

        // Executor - handle various cases
        let executor = 'Unknown';
        if (entry.executor) {
            if (entry.executor.username) {
                // New username format
                executor = `<@${entry.executor.id}> (${entry.executor.username})`;
            } else if (entry.executor.tag) {
                // Legacy tag format
                executor = `<@${entry.executor.id}> (${entry.executor.tag})`;
            } else {
                // Fallback to just mention
                executor = `<@${entry.executor.id}>`;
            }
        }

        // Target - handle various cases and use mentions when possible
        let target = 'Unknown';

        // Debug logging for unknown targets
        if (!entry.target || (!entry.targetType && !entry.target.id)) {
            console.log(`[guildAuditLogEntryCreate] Unknown target for action ${entry.action} (${actionName}):`, {
                targetType: entry.targetType,
                target: entry.target,
                hasTarget: !!entry.target,
                targetKeys: entry.target ? Object.keys(entry.target) : null
            });
        }

        if ((entry.targetType === 'User' || entry.targetType === 'USER') && entry.target) {
            // For users, always try to mention them (works even if not in server with Components v2)
            if (entry.target.username) {
                target = `<@${entry.target.id}> (${entry.target.username})`;
            } else if (entry.target.tag) {
                target = `<@${entry.target.id}> (${entry.target.tag})`;
            } else {
                target = `<@${entry.target.id}>`;
            }
        } else if ((entry.targetType === 'Guild' || entry.targetType === 'GUILD') && entry.target) {
            target = entry.target.name || entry.target.id;
        } else if ((entry.targetType === 'Channel' || entry.targetType === 'CHANNEL') && entry.target) {
            target = `<#${entry.target.id}>`;
        } else if ((entry.targetType === 'Thread' || entry.targetType === 'THREAD') && entry.target) {
            // Threads can be mentioned like channels
            target = `<#${entry.target.id}> (${entry.target.name || 'Unknown Thread'})`;
        } else if ((entry.targetType === 'Role' || entry.targetType === 'ROLE') && entry.target) {
            target = `<@&${entry.target.id}>`;
        } else if ((entry.targetType === 'Message' || entry.targetType === 'MESSAGE') && entry.target) {
            // Message deletions - target is the message author
            if (entry.target.username) {
                target = `<@${entry.target.id}> (${entry.target.username})`;
            } else if (entry.target.tag) {
                target = `<@${entry.target.id}> (${entry.target.tag})`;
            } else {
                target = `<@${entry.target.id}>`;
            }
        } else if ((entry.targetType === 'Invite' || entry.targetType === 'INVITE') && entry.target) {
            // Invites don't have mentions, show code if available
            target = entry.target.code ? `Invite: ${entry.target.code}` : entry.target.id;
        } else if (entry.target && entry.target.id) {
            // Fallback - try to determine type from target properties
            if (entry.target.username || entry.target.tag) {
                // Looks like a user
                target = entry.target.username
                    ? `<@${entry.target.id}> (${entry.target.username})`
                    : `<@${entry.target.id}> (${entry.target.tag})`;
            } else {
                target = entry.target.id;
            }
        }



        // Build detailed change descriptions
        const changeDescriptions = [];
        if (entry.changes && entry.changes.length > 0) {
            for (const change of entry.changes) {
                const description = formatChange(change, entry.action);
                if (description) {
                    changeDescriptions.push(description);
                }
            }
        }

        // Only proceed if there are channels configured for this event
        if (channels.length > 0) {
            guildAuditLogMetrics.containersCreated++;

            // Create Components v2 container for audit log entry
            const container = createAuditLogContainer({
                actionName: actionName,
                executor: executor,
                target: target,
                changes: changeDescriptions,
                reason: entry.reason
            });

            // Send container to all configured channels
            await sendLogContainer(guild.id, 'guildAuditLogEntryCreate', container, client);
        }
        } catch (error) {
            console.error('[guildAuditLogEntryCreate] Error processing audit log entry:', error);
            logger.error('guildAuditLogEntryCreate', `Error processing audit log entry: ${error.message}`, client);
        } finally {
            // Track performance metrics
            const duration = Date.now() - startTime;
            if (guildAuditLogMetrics.verboseLogging || duration > 100) {
                console.log(`[guildAuditLogEntryCreate] ✅ Audit log entry processed in ${duration}ms`);
            }
        }
    },

    // Enhanced optimization functions
    getCachedAuditLogGuildConfig,
    getCachedAuditLogChannels,
    getCachedEventType,
    getGuildAuditLogStats,
    performanceCleanupAndOptimization,
    clearAllGuildAuditLogCaches
};

/**
 * Get comprehensive guild audit log performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive guild audit log performance data
 */
function getGuildAuditLogStats() {
    const cacheHitRate = guildAuditLogMetrics.cacheHits + guildAuditLogMetrics.cacheMisses > 0 ?
        (guildAuditLogMetrics.cacheHits / (guildAuditLogMetrics.cacheHits + guildAuditLogMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: guildAuditLogMetrics.cacheHits,
            cacheMisses: guildAuditLogMetrics.cacheMisses,
            databaseQueries: guildAuditLogMetrics.databaseQueries,
            averageQueryTime: `${guildAuditLogMetrics.averageQueryTime.toFixed(2)}ms`,
            auditLogEntriesProcessed: guildAuditLogMetrics.auditLogEntriesProcessed,
            containersCreated: guildAuditLogMetrics.containersCreated,
            loggingChannelsFound: guildAuditLogMetrics.loggingChannelsFound,
            partialFailures: guildAuditLogMetrics.partialFailures,
            lastOptimization: new Date(guildAuditLogMetrics.lastOptimization).toISOString()
        },

        // Event type breakdown
        eventTypes: guildAuditLogMetrics.eventTypesProcessed,

        // Cache statistics
        caches: {
            guildConfig: auditLogGuildConfigCache.getStats(),
            eventType: auditLogEventTypeCache.getStats(),
            channels: auditLogChannelCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildConfig: auditLogGuildConfigCache.getStats().memoryUsage,
            eventType: auditLogEventTypeCache.getStats().memoryUsage,
            channels: auditLogChannelCache.getStats().memoryUsage,
            total: auditLogGuildConfigCache.getStats().memoryUsage +
                   auditLogEventTypeCache.getStats().memoryUsage +
                   auditLogChannelCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    guildAuditLogMetrics.lastOptimization = Date.now();

    const stats = getGuildAuditLogStats();
    if (guildAuditLogMetrics.verboseLogging) {
        console.log(`[guildAuditLogEntryCreate] 📊 Performance Report:`);
        console.log(`[guildAuditLogEntryCreate]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[guildAuditLogEntryCreate]   Audit Log Entries Processed: ${stats.performance.auditLogEntriesProcessed}`);
        console.log(`[guildAuditLogEntryCreate]   Containers Created: ${stats.performance.containersCreated}`);
        console.log(`[guildAuditLogEntryCreate]   Logging Channels Found: ${stats.performance.loggingChannelsFound}`);
        console.log(`[guildAuditLogEntryCreate]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[guildAuditLogEntryCreate]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[guildAuditLogEntryCreate]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[guildAuditLogEntryCreate]   System Health: ${stats.systemHealth.status}`);
        console.log(`[guildAuditLogEntryCreate]   Event Types: ${Object.keys(stats.eventTypes).length} different types processed`);
    }

    return stats;
}

/**
 * Clear all guild audit log caches (Enterprise-Grade Cache Management)
 */
function clearAllGuildAuditLogCaches() {
    auditLogGuildConfigCache.clear();
    auditLogEventTypeCache.clear();
    auditLogChannelCache.clear();

    console.log('[guildAuditLogEntryCreate] 🗑️ Cleared all guild audit log caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, guildAuditLogMetrics.performanceReportInterval);