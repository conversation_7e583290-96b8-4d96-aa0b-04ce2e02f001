const { Events } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne } = require('../utils/database-optimizer.js');
const { defaults } = require("../utils/default_db_structures");
const { EmbedBuilder } = require('discord.js');
const { safeHandleDehoist } = require('../utils/dehoistRateLimit');
const { sendStickyNicknameRecoveredLog, sendStickyRolesRecoveredLog, sendLogContainer } = require("../utils/sendLog.js");
const { createMemberJoinContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');

/**
 * Guild Member Add Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes member join processing with comprehensive caching and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const guildMemberAddMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    memberJoinsProcessed: 0,
    guildDataFetches: 0,
    memberDataCreations: 0,
    stickyRoleOperations: 0,
    dehoistOperations: 0,
    parallelOperations: 0,
    partialFailures: 0,
    loggingOperations: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations
const memberDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for member data
const stickyConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for sticky configurations
const defaultDataCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for default structures

// Register caches for global cleanup
registerCache(guildConfigCache);
registerCache(memberDataCache);
registerCache(stickyConfigCache);
registerCache(defaultDataCache);

/**
 * Get cached guild configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild configuration data
 */
async function getCachedGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `guild_config_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildConfigCache.get(cacheKey);
        if (cached) {
            guildMemberAddMetrics.cacheHits++;
            if (guildMemberAddMetrics.verboseLogging) {
                console.log(`[guildMemberAdd] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildMemberAddMetrics.cacheMisses++;
        guildMemberAddMetrics.databaseQueries++;
        guildMemberAddMetrics.guildDataFetches++;

        // Get guild data with fallback creation
        let guildData = await optimizedFindOne('guilds', { id: guildId });
        if (!guildData) {
            const defaultGuild = await getCachedDefaultGuild(guildId);
            await optimizedInsertOne('guilds', defaultGuild);
            guildData = await optimizedFindOne('guilds', { id: guildId });
            guildMemberAddMetrics.databaseQueries += 2; // Insert + re-fetch
        }

        // Cache the result
        guildConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        guildMemberAddMetrics.averageQueryTime =
            (guildMemberAddMetrics.averageQueryTime * (guildMemberAddMetrics.databaseQueries - 1) + duration) /
            guildMemberAddMetrics.databaseQueries;

        if (guildMemberAddMetrics.verboseLogging || duration > 100) {
            console.log(`[guildMemberAdd] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error(`[guildMemberAdd] ❌ Error getting guild config for ${guildId}:`, error);
        // Return default guild structure on error
        return await getCachedDefaultGuild(guildId);
    }
}

/**
 * Get cached member data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching for member data with creation fallback
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Member data
 */
async function getCachedMemberData(userId, guildId) {
    const startTime = Date.now();
    const cacheKey = `member_${userId}_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = memberDataCache.get(cacheKey);
        if (cached) {
            guildMemberAddMetrics.cacheHits++;
            if (guildMemberAddMetrics.verboseLogging) {
                console.log(`[guildMemberAdd] ⚡ Member data cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildMemberAddMetrics.cacheMisses++;
        guildMemberAddMetrics.databaseQueries++;

        // Get member data with fallback creation
        let memberData = await optimizedFindOne('member', { userId: userId });
        if (!memberData) {
            const defaultMember = await getCachedDefaultMember({ guildId, userId });
            await optimizedInsertOne('member', defaultMember);
            memberData = await optimizedFindOne('member', { userId: userId });
            guildMemberAddMetrics.databaseQueries += 2; // Insert + re-fetch
            guildMemberAddMetrics.memberDataCreations++;
        }

        // Cache the result
        memberDataCache.set(cacheKey, memberData);

        const duration = Date.now() - startTime;
        guildMemberAddMetrics.averageQueryTime =
            (guildMemberAddMetrics.averageQueryTime * (guildMemberAddMetrics.databaseQueries - 1) + duration) /
            guildMemberAddMetrics.databaseQueries;

        if (guildMemberAddMetrics.verboseLogging || duration > 50) {
            console.log(`[guildMemberAdd] ✅ Member data fetched for ${userId}: ${duration}ms - cached for future access`);
        }

        return memberData;
    } catch (error) {
        console.error(`[guildMemberAdd] ❌ Error getting member data for ${userId}:`, error);
        // Return default member structure on error
        return await getCachedDefaultMember({ guildId, userId });
    }
}

/**
 * Get cached sticky configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching for sticky role configurations
 * @param {Object} guildData - Guild data object
 * @returns {Object} Sticky configuration
 */
function getCachedStickyConfig(guildData) {
    const startTime = Date.now();
    const cacheKey = `sticky_config_${guildData.id}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = stickyConfigCache.get(cacheKey);
        if (cached) {
            guildMemberAddMetrics.cacheHits++;
            if (guildMemberAddMetrics.verboseLogging) {
                console.log(`[guildMemberAdd] ⚡ Sticky config cache hit for ${guildData.id} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildMemberAddMetrics.cacheMisses++;

        // Extract sticky configuration
        const stickyConfig = guildData.sticky || { enabled: false, roles: [], nick: false };

        // Cache the result
        stickyConfigCache.set(cacheKey, stickyConfig);

        const duration = Date.now() - startTime;
        if (guildMemberAddMetrics.verboseLogging || duration > 10) {
            console.log(`[guildMemberAdd] ✅ Sticky config processed for ${guildData.id}: ${duration}ms - cached for future access`);
        }

        return stickyConfig;
    } catch (error) {
        console.error(`[guildMemberAdd] ❌ Error processing sticky config:`, error);
        return { enabled: false, roles: [], nick: false };
    }
}

/**
 * Get cached default guild structure (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching for default data structures
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Default guild structure
 */
async function getCachedDefaultGuild(guildId) {
    const cacheKey = `default_guild_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = defaultDataCache.get(cacheKey);
        if (cached) {
            guildMemberAddMetrics.cacheHits++;
            return cached;
        }

        guildMemberAddMetrics.cacheMisses++;

        // Generate default guild structure
        const defaultGuild = defaults.guild(guildId);

        // Cache the result
        defaultDataCache.set(cacheKey, defaultGuild);

        return defaultGuild;
    } catch (error) {
        console.error(`[guildMemberAdd] ❌ Error generating default guild:`, error);
        return defaults.guild(guildId);
    }
}

/**
 * Get cached default member structure (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching for default member data structures
 * @param {Object} params - Parameters object with guildId and userId
 * @returns {Promise<Object>} Default member structure
 */
async function getCachedDefaultMember(params) {
    const cacheKey = `default_member_${params.guildId}_${params.userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = defaultDataCache.get(cacheKey);
        if (cached) {
            guildMemberAddMetrics.cacheHits++;
            return cached;
        }

        guildMemberAddMetrics.cacheMisses++;

        // Generate default member structure
        const defaultMember = defaults.member(params);

        // Cache the result (shorter TTL for member-specific data)
        defaultDataCache.set(cacheKey, defaultMember);

        return defaultMember;
    } catch (error) {
        console.error(`[guildMemberAdd] ❌ Error generating default member:`, error);
        return defaults.member(params);
    }
}

module.exports = {
    name: Events.GuildMemberAdd,
    async execute(client, member) {
        const startTime = Date.now();

        try {
            console.log('[guildMemberAdd] Event triggered for', member.user.tag);
            if (mongoClient.clientConnected == false) {
                console.log("mongodb not ready yet");
                return;
            }

            // Track member join processing
            guildMemberAddMetrics.memberJoinsProcessed++;

            // OPTIMIZED: Enhanced parallel operations with cached data fetching
            const [guildDataResult, memberDataResult] = await Promise.allSettled([
                getCachedGuildConfig(member.guild.id),
                getCachedMemberData(member.id, member.guild.id)
            ]);

            // Track parallel operation
            guildMemberAddMetrics.parallelOperations++;

            // Handle results with graceful fallbacks
            const guildData = guildDataResult.status === 'fulfilled' ? guildDataResult.value : await getCachedDefaultGuild(member.guild.id);
            const userData = memberDataResult.status === 'fulfilled' ? memberDataResult.value : await getCachedDefaultMember({ guildId: member.guild.id, userId: member.id });

            // Track partial failures
            const failures = [guildDataResult, memberDataResult].filter(r => r.status === 'rejected');
            if (failures.length > 0) {
                guildMemberAddMetrics.partialFailures++;
                if (guildMemberAddMetrics.verboseLogging) {
                    console.log(`[guildMemberAdd] ⚠️ ${failures.length} partial failures in parallel data fetching for ${member.user.tag}`);
                }
            }

            // OPTIMIZED: Enhanced processing with cached sticky configuration
            const stickyConfig = getCachedStickyConfig(guildData);

            // OPTIMIZED: Parallel processing of dehoist and logging operations
            const [dehoistResult, loggingResult] = await Promise.allSettled([
                // Handle dehoist for new members
                (async () => {
                    if (guildData.dehoist && guildData.dehoist.enabled) { // Use standard enabled field
                        guildMemberAddMetrics.dehoistOperations++;
                        const result = await safeHandleDehoist(member, guildData.dehoist);
                        if (result.success && result.cooldown) {
                            console.log(`[guildMemberAdd] New member ${member.user.id} dehoisted - ${result.cooldown}min cooldown applied`);
                        } else if (result.reason === 'guild_throttle') {
                            console.log(`[guildMemberAdd] Guild ${member.guild.id} dehoist throttled for new member`);
                        }
                        return result;
                    }
                    return { success: false, reason: 'disabled' };
                })(),

                // Standard join logging
                (async () => {
                    if (guildData.logs && guildData.logs.enabled) {
                        guildMemberAddMetrics.loggingOperations++;
                        console.log('[guildMemberAdd] Logging is enabled');
                        const channels = guildData.logs.channels.filter(l => l.events.includes("guildMemberAdd")).map(l => member.guild.channels.cache.get(l.id)).filter(ch => ch);

                        // Only proceed if there are channels configured for this event
                        if (channels.length > 0) {
                            // Create Components v2 container for member join
                            const container = createMemberJoinContainer({
                                userMention: `<@${member.id}>`,
                                userTag: member.user.tag,
                                userId: member.id,
                                accountAge: `<t:${Math.floor(member.user.createdTimestamp / 1000)}:R>`,
                                memberCount: member.guild.memberCount
                            });

                            // Send container to all configured channels
                            await sendLogContainer(member.guild.id, 'guildMemberAdd', container, client);
                            return { success: true, channels: channels.length };
                        }
                    }
                    return { success: false, reason: 'disabled_or_no_channels' };
                })()
            ]);

            // Track parallel operation
            guildMemberAddMetrics.parallelOperations++;

            // Handle partial failures for dehoist and logging
            const processingFailures = [dehoistResult, loggingResult].filter(r => r.status === 'rejected');
            if (processingFailures.length > 0) {
                guildMemberAddMetrics.partialFailures++;
                if (guildMemberAddMetrics.verboseLogging) {
                    console.log(`[guildMemberAdd] ⚠️ ${processingFailures.length} partial failures in dehoist/logging processing for ${member.user.tag}`);
                }
            }

            // OPTIMIZED: Enhanced sticky role processing with cached configuration
            const {
                getCachedGuildStickyConfig,
                getCachedMemberStickyData,
                getAssignableRoles
            } = require('../utils/stickyCache.js');

            const cachedStickyConfig = await getCachedGuildStickyConfig(member.guild.id);
            console.log('[sticky] Guild sticky config:', cachedStickyConfig);
            guildMemberAddMetrics.stickyRoleOperations++;

            if (cachedStickyConfig.enabled && (cachedStickyConfig.roles.length || cachedStickyConfig.nick)) {
                console.log('[sticky] Sticky is enabled and configured, checking member data...');
                const memberStickyData = await getCachedMemberStickyData(member.user.id, member.guild.id);
                const { roles, nick } = memberStickyData;
                console.log('[sticky] Member sticky data:', memberStickyData);

                // Filter saved roles to only include currently sticky roles
                const currentlyStickyRoles = (roles || []).filter(roleId =>
                    cachedStickyConfig.roles.includes(roleId)
                );

                // Use cached role validation for better performance
                const assignableRoles = getAssignableRoles(currentlyStickyRoles, member.guild.id, member.guild);

                console.log('[sticky] Restoring for', member.user.tag, 'saved roles:', roles, 'currently sticky:', currentlyStickyRoles, 'assignable:', assignableRoles, 'nick:', nick);

                // Apply sticky data
                const editData = {};
                if (cachedStickyConfig.roles.length && assignableRoles.length) {
                    editData.roles = assignableRoles;
                }
                if (cachedStickyConfig.nick && nick) {
                    editData.nick = nick;
                }

                if (Object.keys(editData).length > 0) {
                    console.log('[sticky] Applying edit data:', editData);
                    member.edit(editData).catch(err => console.error('[sticky] Error restoring:', err));

                    // OPTIMIZED: Parallel sticky logging operations
                    const stickyLoggingPromises = [];
                    if (editData.roles) {
                        stickyLoggingPromises.push(
                            sendStickyRolesRecoveredLog(
                                member.guild.id,
                                member.id,
                                assignableRoles,
                                client
                            ).catch(err => console.error('[sticky] Error sending roles log:', err))
                        );
                    }
                    if (editData.nick) {
                        stickyLoggingPromises.push(
                            sendStickyNicknameRecoveredLog(
                                member.guild.id,
                                member.id,
                                nick,
                                client
                            ).catch(err => console.error('[sticky] Error sending nick log:', err))
                        );
                    }

                    // Execute sticky logging in parallel
                    if (stickyLoggingPromises.length > 0) {
                        Promise.allSettled(stickyLoggingPromises).then(results => {
                            const logFailures = results.filter(r => r.status === 'rejected');
                            if (logFailures.length > 0 && guildMemberAddMetrics.verboseLogging) {
                                console.log(`[guildMemberAdd] ⚠️ ${logFailures.length} sticky logging failures for ${member.user.tag}`);
                            }
                        });
                    }
                } else {
                    console.log('[sticky] No edit data to apply');
                }
            } else {
                console.log('[sticky] Sticky not enabled or not configured - enabled:', cachedStickyConfig.enabled, 'roles:', cachedStickyConfig.roles.length, 'nick:', cachedStickyConfig.nick);
            }

        } catch (error) {
            console.error('[guildMemberAdd] Error processing member add:', error);
            logger.error('guildMemberAdd', `Error processing member add: ${error.message}`, client);
        } finally {
            // Track performance metrics
            const duration = Date.now() - startTime;
            if (guildMemberAddMetrics.verboseLogging || duration > 100) {
                console.log(`[guildMemberAdd] ✅ Member join processed for ${member.user.tag} in ${duration}ms`);
            }
        }
    },

    // Enhanced optimization functions
    getCachedGuildConfig,
    getCachedMemberData,
    getCachedStickyConfig,
    getCachedDefaultGuild,
    getCachedDefaultMember
};

/**
 * Get comprehensive guild member add system performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive guild member add system performance data
 */
function getGuildMemberAddStats() {
    const cacheHitRate = guildMemberAddMetrics.cacheHits + guildMemberAddMetrics.cacheMisses > 0 ?
        (guildMemberAddMetrics.cacheHits / (guildMemberAddMetrics.cacheHits + guildMemberAddMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: guildMemberAddMetrics.cacheHits,
            cacheMisses: guildMemberAddMetrics.cacheMisses,
            databaseQueries: guildMemberAddMetrics.databaseQueries,
            averageQueryTime: `${guildMemberAddMetrics.averageQueryTime.toFixed(2)}ms`,
            memberJoinsProcessed: guildMemberAddMetrics.memberJoinsProcessed,
            guildDataFetches: guildMemberAddMetrics.guildDataFetches,
            memberDataCreations: guildMemberAddMetrics.memberDataCreations,
            stickyRoleOperations: guildMemberAddMetrics.stickyRoleOperations,
            dehoistOperations: guildMemberAddMetrics.dehoistOperations,
            parallelOperations: guildMemberAddMetrics.parallelOperations,
            partialFailures: guildMemberAddMetrics.partialFailures,
            loggingOperations: guildMemberAddMetrics.loggingOperations,
            lastOptimization: new Date(guildMemberAddMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildConfig: guildConfigCache.getStats(),
            memberData: memberDataCache.getStats(),
            stickyConfig: stickyConfigCache.getStats(),
            defaultData: defaultDataCache.getStats()
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization',
            parallelEfficiency: guildMemberAddMetrics.parallelOperations > 0 ?
                ((guildMemberAddMetrics.parallelOperations - guildMemberAddMetrics.partialFailures) / guildMemberAddMetrics.parallelOperations * 100).toFixed(2) + '%' : 'N/A'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    guildMemberAddMetrics.lastOptimization = Date.now();

    const stats = getGuildMemberAddStats();
    if (guildMemberAddMetrics.verboseLogging) {
        console.log(`[guildMemberAdd] 📊 Performance Report:`);
        console.log(`[guildMemberAdd]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[guildMemberAdd]   Member Joins Processed: ${stats.performance.memberJoinsProcessed}`);
        console.log(`[guildMemberAdd]   Guild Data Fetches: ${stats.performance.guildDataFetches}`);
        console.log(`[guildMemberAdd]   Member Data Creations: ${stats.performance.memberDataCreations}`);
        console.log(`[guildMemberAdd]   Sticky Role Operations: ${stats.performance.stickyRoleOperations}`);
        console.log(`[guildMemberAdd]   Dehoist Operations: ${stats.performance.dehoistOperations}`);
        console.log(`[guildMemberAdd]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[guildMemberAdd]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[guildMemberAdd]   Logging Operations: ${stats.performance.loggingOperations}`);
        console.log(`[guildMemberAdd]   Parallel Efficiency: ${stats.systemHealth.parallelEfficiency}`);
        console.log(`[guildMemberAdd]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[guildMemberAdd]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Invalidate guild configuration cache
 * @param {string} guildId - Guild ID
 */
function invalidateGuildConfigCache(guildId) {
    const cacheKey = `guild_config_${guildId}`;
    guildConfigCache.delete(cacheKey);

    // Also invalidate related sticky configuration cache
    const stickyCacheKey = `sticky_config_${guildId}`;
    stickyConfigCache.delete(stickyCacheKey);

    if (guildMemberAddMetrics.verboseLogging) {
        console.log(`[guildMemberAdd] 🗑️ Invalidated guild config and sticky config caches for ${guildId}`);
    }
}

/**
 * Invalidate member data cache
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional)
 */
function invalidateMemberDataCache(userId, guildId = null) {
    if (guildId) {
        const cacheKey = `member_${userId}_${guildId}`;
        memberDataCache.delete(cacheKey);
    } else {
        // Clear all member data caches for this user
        const keys = Array.from(memberDataCache.keys());
        const userKeys = keys.filter(key => key.includes(`member_${userId}_`));

        userKeys.forEach(key => {
            memberDataCache.delete(key);
        });
    }

    if (guildMemberAddMetrics.verboseLogging) {
        console.log(`[guildMemberAdd] 🗑️ Invalidated member data cache for ${userId}${guildId ? ` in ${guildId}` : ' (all guilds)'}`);
    }
}

/**
 * Invalidate sticky configuration cache
 * @param {string} guildId - Guild ID
 */
function invalidateStickyConfigCache(guildId) {
    const cacheKey = `sticky_config_${guildId}`;
    stickyConfigCache.delete(cacheKey);

    if (guildMemberAddMetrics.verboseLogging) {
        console.log(`[guildMemberAdd] 🗑️ Invalidated sticky config cache for ${guildId}`);
    }
}

/**
 * Invalidate default data cache
 * @param {string} type - Type of default data ('guild' or 'member')
 * @param {string} id - ID to invalidate
 */
function invalidateDefaultDataCache(type, id) {
    if (type === 'guild') {
        const cacheKey = `default_guild_${id}`;
        defaultDataCache.delete(cacheKey);
    } else if (type === 'member') {
        // Clear all member default caches for this ID pattern
        const keys = Array.from(defaultDataCache.keys());
        const memberKeys = keys.filter(key => key.includes(`default_member_`) && key.includes(id));

        memberKeys.forEach(key => {
            defaultDataCache.delete(key);
        });
    }

    if (guildMemberAddMetrics.verboseLogging) {
        console.log(`[guildMemberAdd] 🗑️ Invalidated default ${type} cache for ${id}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, guildMemberAddMetrics.performanceReportInterval);

// Export performance functions for external monitoring
module.exports.getGuildMemberAddStats = getGuildMemberAddStats;
module.exports.invalidateGuildConfigCache = invalidateGuildConfigCache;
module.exports.invalidateMemberDataCache = invalidateMemberDataCache;
module.exports.invalidateStickyConfigCache = invalidateStickyConfigCache;
module.exports.invalidateDefaultDataCache = invalidateDefaultDataCache;