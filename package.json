{"name": "17", "version": "1.0.0", "description": "a bot from ??", "main": "index.js", "scripts": {"test": "node tests/comprehensive-test-runner.js", "test:basic": "node tests/run-interaction-tests.js", "test:scan": "node -e \"const scanner = require('./tests/interaction-scanner.js'); const s = new scanner(); s.scanCodebase().then(() => s.printResults());\"", "test:watch": "nodemon --watch . --ext js --exec \"npm test\"", "postinstall": "echo '📦 Dependencies installed! For transcription to work, ensure Python and openai-whisper are installed: pip install openai-whisper'"}, "author": "", "license": "ISC", "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "axios": "^1.6.2", "discord.js": "^14.21.0", "dotenv": "^16.4.5", "fluent-ffmpeg": "^2.1.2", "fs-extra": "^11.2.0", "mongodb": "^6.10.0", "node-fetch": "^3.3.2", "openai-whisper": "^1.0.2"}}