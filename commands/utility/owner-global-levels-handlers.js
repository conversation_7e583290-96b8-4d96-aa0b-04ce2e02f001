const { Con<PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { mongoClient } = require('../../mongo/client.js');
const { optimizedInsertOne, optimizedUpdateOne } = require('../../utils/database-optimizer.js');
const { ObjectId } = require('mongodb');
const { getRecentImagesFromChannel, uploadImageAsEmote } = require('../../utils/imageUploader.js');
const { markEmojiAsSaved } = require('../../utils/emojiCleanup.js');
const { buildGlobalLevelsContainer, showGlobalLevelCreationInterface, showGlobalLevelEditInterface, generateExpOptions, tempGlobalLevelState } = require('./owner-global-levels.js');
const { OPERATION_COLORS, LOG_COLORS } = require('../../utils/colors.js');

/**
 * Global Levels Handlers Module
 * Contains all the select menu and button handlers for global levels
 * Separated from main module to keep file sizes manageable
 */

// safeInteractionUpdate helper removed - functions now return components directly

/**
 * Handle global level config select (creation)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected value
 */
async function handleGlobalLevelConfigSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    switch (value) {
        case 'name':
            await showGlobalLevelNameModal(interaction);
            return []; // Modal shown, no components to return
        case 'exp':
            state.currentConfig = 'exp';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);
        case 'level_icon':
            state.currentConfig = 'level_icon';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);
        case 'prestige_icon':
            state.currentConfig = 'prestige_icon';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);
        case 'items':
            state.currentConfig = 'items';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);
        case 'xp_boosters':
            state.currentConfig = 'xp_boosters';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);
        case 'drop_boosters':
            state.currentConfig = 'drop_boosters';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);
        case 'stars':
            state.currentConfig = 'stars';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);

        default:
            state.currentConfig = null;
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelCreationInterface(interaction);
    }
}

/**
 * Handle global level edit config select
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected value
 */
async function handleGlobalLevelEditConfigSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    switch (value) {
        case 'name':
            await showGlobalLevelEditNameModal(interaction);
            return; // Modal shown, no container to return
        case 'exp':
            state.currentConfig = 'exp';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        case 'level_icon':
            state.currentConfig = 'level_icon';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        case 'prestige_icon':
            state.currentConfig = 'prestige_icon';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        case 'items':
            state.currentConfig = 'items';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        case 'xp_boosters':
            state.currentConfig = 'xp_boosters';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        case 'drop_boosters':
            state.currentConfig = 'drop_boosters';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        case 'stars':
            state.currentConfig = 'stars';
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        default:
            state.currentConfig = null;
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
    }
}

/**
 * Show global level name modal (creation)
 * @param {Object} interaction - Discord interaction
 */
async function showGlobalLevelNameModal(interaction) {
    const modal = new ModalBuilder()
        .setCustomId('global-level-name-modal')
        .setTitle('Set Global Level Name');

    const nameInput = new TextInputBuilder()
        .setCustomId('level-name')
        .setLabel('Level Name')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter level name...')
        .setMaxLength(32)
        .setRequired(true);

    const nameRow = new ActionRowBuilder().addComponents(nameInput);
    modal.addComponents(nameRow);

    await interaction.showModal(modal);
}

/**
 * Show global level edit name modal
 * @param {Object} interaction - Discord interaction
 */
async function showGlobalLevelEditNameModal(interaction) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);
    const currentName = state?.name || '';

    const modal = new ModalBuilder()
        .setCustomId('global-level-edit-name-modal')
        .setTitle('Edit Global Level Name');

    const nameInput = new TextInputBuilder()
        .setCustomId('level-name')
        .setLabel('Level Name')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter level name...')
        .setMaxLength(32)
        .setRequired(true)
        .setValue(currentName); // Set current name as default

    const nameRow = new ActionRowBuilder().addComponents(nameInput);
    modal.addComponents(nameRow);

    await interaction.showModal(modal);
}

/**
 * Handle EXP selection (creation)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected EXP value
 */
async function handleGlobalLevelExpSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    state.expRequired = parseInt(value);
    state.currentConfig = null; // Clear current config to hide the exp select
    tempGlobalLevelState.set(stateKey, state);
    return await showGlobalLevelCreationInterface(interaction);
}

/**
 * Handle EXP selection (editing)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected EXP value
 */
async function handleGlobalLevelEditExpSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    state.expRequired = parseInt(value);
    state.currentConfig = null; // Clear current config to hide the exp select
    tempGlobalLevelState.set(stateKey, state);
    return await showGlobalLevelEditInterface(interaction);
}

/**
 * Handle icon selection (creation)
 * FIXED: Properly handle "no-images" selection without losing create state
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected image value
 */
async function handleGlobalLevelIconSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    // FIXED: Handle "no-images" selection properly
    if (value === 'no-images') {
        // Clear current config and return to creation interface
        state.currentConfig = null;
        tempGlobalLevelState.set(stateKey, state);
        await showGlobalLevelCreationInterface(interaction);
        return;
    }

    try {
        const selectedImageIndex = parseInt(value.replace('image-', ''));

        // CRITICAL FIX: Get fresh images to ensure newly uploaded images are available
        const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
        const recentImages = await forceRefreshImageCache(interaction);
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            // FIXED: Return to creation interface instead of main container to preserve create state
            state.currentConfig = null;
            tempGlobalLevelState.set(stateKey, state);
            await showGlobalLevelCreationInterface(interaction);
            return;
        }

        // UPDATED: Upload image as custom emote (application emote for consistency)
        const emoteData = await uploadImageAsEmote(
            selectedImage.url,
            selectedImage.filename,
            interaction.guild.id,
            interaction.client,
            { useApplicationEmote: true } // Explicitly use application emojis
        );

        if (!emoteData) {
            // Build container with status message instead of ephemeral reply
            const container = await buildGlobalLevelsContainer();
            const statusContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** Failed to upload image as emote. Please try again.'))
                .setAccentColor(LOG_COLORS.ERROR);

            return [container, statusContainer];
            return;
        }

        // Store the emote string in state
        state.levelIcon = emoteData.string;
        state.currentConfig = null; // Clear to hide image select menu
        tempGlobalLevelState.set(stateKey, state);

        // Rebuild interface
        await showGlobalLevelCreationInterface(interaction);

    } catch (error) {
        console.error('[handleGlobalLevelIconSelect] Error:', error);
        // Return error container instead of direct reply
        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ An error occurred while processing the image. Please try again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
    }
}

/**
 * Handle icon selection (editing)
 * FIXED: Properly handle "no-images" selection without losing edit state
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected image value
 */
async function handleGlobalLevelEditIconSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    // FIXED: Handle "no-images" selection properly
    if (value === 'no-images') {
        // Clear current config and return to edit interface
        state.currentConfig = null;
        tempGlobalLevelState.set(stateKey, state);
        return await showGlobalLevelEditInterface(interaction);
    }

    try {
        const selectedImageIndex = parseInt(value.replace('image-', ''));

        // CRITICAL FIX: Get fresh images to ensure newly uploaded images are available
        const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
        const recentImages = await forceRefreshImageCache(interaction);
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            // FIXED: Return to edit interface instead of main container to preserve edit state
            state.currentConfig = null;
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        }

        // UPDATED: Upload image as custom emote (application emote for consistency)
        const emoteData = await uploadImageAsEmote(
            selectedImage.url,
            selectedImage.filename,
            interaction.guild.id,
            interaction.client,
            { useApplicationEmote: true } // Explicitly use application emojis
        );

        if (!emoteData) {
            // Build container with status message instead of ephemeral reply
            const container = await buildGlobalLevelsContainer();
            const statusContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** Failed to upload image as emote. Please try again.'))
                .setAccentColor(LOG_COLORS.ERROR);

            await safeInteractionUpdate(interaction, {
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container, statusContainer]
            });
            return;
        }

        // Store the emote string in state
        state.levelIcon = emoteData.string;
        state.currentConfig = null; // Clear to hide image select menu
        tempGlobalLevelState.set(stateKey, state);

        // Rebuild interface
        return await showGlobalLevelEditInterface(interaction);

    } catch (error) {
        console.error('[handleGlobalLevelEditIconSelect] Error:', error);
        // Build container with status message instead of ephemeral reply
        const container = await buildGlobalLevelsContainer();
        const statusContainer = new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** An error occurred while processing the image. Please try again.'))
            .setAccentColor(LOG_COLORS.ERROR);

        await safeInteractionUpdate(interaction, {
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container, statusContainer]
        });
    }
}

/**
 * Handle prestige icon selection (creation)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected image value
 */
/**
 * Handle prestige icon selection (creation)
 * FIXED: Properly handle "no-images" selection without losing create state
 */
async function handleGlobalPrestigeIconSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    // FIXED: Handle "no-images" selection properly
    if (value === 'no-images') {
        // Clear current config and return to creation interface
        state.currentConfig = null;
        tempGlobalLevelState.set(stateKey, state);
        await showGlobalLevelCreationInterface(interaction);
        return;
    }

    try {
        const selectedImageIndex = parseInt(value.replace('image-', ''));

        // CRITICAL FIX: Get fresh images to ensure newly uploaded images are available
        const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
        const recentImages = await forceRefreshImageCache(interaction);
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            // FIXED: Return to creation interface instead of ephemeral reply to preserve create state
            state.currentConfig = null;
            tempGlobalLevelState.set(stateKey, state);
            await showGlobalLevelCreationInterface(interaction);
            return;
        }

        // Upload image as custom emote (application emote for global levels)
        const emoteData = await uploadImageAsEmote(
            selectedImage.url,
            selectedImage.filename,
            interaction.guild.id,
            interaction.client,
            { useApplicationEmote: true }
        );

        if (!emoteData) {
            // Return error container instead of direct reply
            return new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Failed to upload image as emote. Please try again.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
        }

        // Store the emote string in state
        state.prestigeIcon = emoteData.string;
        state.currentConfig = null; // Clear to hide image select menu
        tempGlobalLevelState.set(stateKey, state);

        // Rebuild interface
        await showGlobalLevelCreationInterface(interaction);

    } catch (error) {
        console.error('[handleGlobalPrestigeIconSelect] Error:', error);
        // Return error container instead of direct reply
        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ An error occurred while processing the image. Please try again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
    }
}

/**
 * Handle prestige icon selection (editing)
 * FIXED: Properly handle "no-images" selection without losing edit state
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected image value
 */
async function handleGlobalLevelEditPrestigeIconSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    // FIXED: Handle "no-images" selection properly
    if (value === 'no-images') {
        // Clear current config and return to edit interface
        state.currentConfig = null;
        tempGlobalLevelState.set(stateKey, state);
        return await showGlobalLevelEditInterface(interaction);
    }

    try {
        const selectedImageIndex = parseInt(value.replace('image-', ''));

        // CRITICAL FIX: Get fresh images to ensure newly uploaded images are available
        const { forceRefreshImageCache } = require('../../utils/imageUploader.js');
        const recentImages = await forceRefreshImageCache(interaction);
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            // FIXED: Return to edit interface instead of ephemeral reply to preserve edit state
            state.currentConfig = null;
            tempGlobalLevelState.set(stateKey, state);
            return await showGlobalLevelEditInterface(interaction);
        }

        // Upload image as custom emote (application emote for global levels)
        const emoteData = await uploadImageAsEmote(
            selectedImage.url,
            selectedImage.filename,
            interaction.guild.id,
            interaction.client,
            { useApplicationEmote: true }
        );

        if (!emoteData) {
            // Return error container instead of direct reply
            return new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ Failed to upload image as emote. Please try again.'))
                .setAccentColor(OPERATION_COLORS.DELETE);
        }

        // Store the emote string in state
        state.prestigeIcon = emoteData.string;
        state.currentConfig = null; // Clear to hide image select menu
        tempGlobalLevelState.set(stateKey, state);



        // Rebuild interface
        return await showGlobalLevelEditInterface(interaction);

    } catch (error) {
        console.error('[handleGlobalLevelEditPrestigeIconSelect] Error:', error);
        // Return error container instead of direct reply
        return new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** ❌ An error occurred while processing the image. Please try again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
    }
}

/**
 * Handle global level creation final button
 * @param {Object} interaction - Discord interaction
 */
async function createGlobalLevel(interaction) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        // Return to main global levels view
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    // Validate required fields
    if (!state.name || state.expRequired === null || state.expRequired === undefined) {
        // Return to creation interface to show current state
        await showGlobalLevelCreationInterface(interaction);
        return;
    }

    try {
        // Create the level
        const levelData = {
            level: state.level,
            name: state.name,
            expRequired: state.expRequired,
            levelIcon: state.levelIcon,
            prestigeIcon: state.prestigeIcon,
            rewards: state.rewards,
            isActive: true,
            createdAt: new Date(),
            createdBy: interaction.user.id
        };

        await optimizedInsertOne('global_levels', levelData);

        // Mark emojis as saved if they exist
        if (state.levelIcon) {
            const levelIconMatch = state.levelIcon.match(/<a?:\w+:(\d+)>/);
            if (levelIconMatch) {
                markEmojiAsSaved(levelIconMatch[1]);
            }
        }
        if (state.prestigeIcon) {
            const prestigeIconMatch = state.prestigeIcon.match(/<a?:\w+:(\d+)>/);
            if (prestigeIconMatch) {
                markEmojiAsSaved(prestigeIconMatch[1]);
            }
        }

        // Clear temp state
        tempGlobalLevelState.delete(stateKey);

        // Clear global levels cache
        const { invalidateAllCaches } = require('../../utils/globalLevels.js');
        invalidateAllCaches();

        // Return to main global levels view with success message (force fresh data)
        const container = await buildGlobalLevelsContainer('main', true);
        return container;

    } catch (error) {
        console.error('[createGlobalLevel] Error:', error);
        // Return to creation interface on error
        await showGlobalLevelCreationInterface(interaction);
    }
}

/**
 * Handle global level update final button
 * @param {Object} interaction - Discord interaction
 */
async function updateGlobalLevel(interaction) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        // Return to main global levels view
        const container = await buildGlobalLevelsContainer();
        return container;
        return;
    }

    try {
        // Update the level
        const updateData = {
            name: state.name,
            expRequired: state.expRequired,
            levelIcon: state.levelIcon,
            prestigeIcon: state.prestigeIcon,
            rewards: state.rewards,
            updatedAt: new Date(),
            updatedBy: interaction.user.id
        };

        const updateResult = await optimizedUpdateOne('global_levels',
            { _id: new ObjectId(state.levelId) },
            { $set: updateData }
        );

        // Mark emojis as saved if they exist
        if (state.levelIcon) {
            const levelIconMatch = state.levelIcon.match(/<a?:\w+:(\d+)>/);
            if (levelIconMatch) {
                markEmojiAsSaved(levelIconMatch[1]);
            }
        }
        if (state.prestigeIcon) {
            const prestigeIconMatch = state.prestigeIcon.match(/<a?:\w+:(\d+)>/);
            if (prestigeIconMatch) {
                markEmojiAsSaved(prestigeIconMatch[1]);
            }
        }

        // Clear temp state
        tempGlobalLevelState.delete(stateKey);

        // Clear global levels cache and force fresh data
        const { invalidateAllCaches } = require('../../utils/globalLevels.js');
        invalidateAllCaches();

        // Wait a moment to ensure cache is cleared
        await new Promise(resolve => setTimeout(resolve, 100));

        // Return to main global levels view with success message (force fresh data)
        const container = await buildGlobalLevelsContainer('main', true);
        return container;

    } catch (error) {
        console.error('[updateGlobalLevel] Error:', error);
        // Return to edit interface on error
        return await showGlobalLevelEditInterface(interaction);
    }
}

/**
 * Handle XP booster selection (creation)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected booster value
 */
async function handleGlobalLevelXpBoosterSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        state.rewards.xpBooster = null;
    } else {
        state.rewards.xpBooster = parseFloat(value);
    }

    state.currentConfig = null; // Clear current config to hide the booster select
    tempGlobalLevelState.set(stateKey, state);
    await showGlobalLevelCreationInterface(interaction);
}

/**
 * Handle XP booster selection (editing)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected booster value
 */
async function handleGlobalLevelEditXpBoosterSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        state.rewards.xpBooster = null;
    } else {
        state.rewards.xpBooster = parseFloat(value);
    }

    state.currentConfig = null; // Clear current config to hide the booster select
    tempGlobalLevelState.set(stateKey, state);
    return await showGlobalLevelEditInterface(interaction);
}

/**
 * Handle drop booster selection (creation)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected booster value
 */
async function handleGlobalLevelDropBoosterSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        state.rewards.dropBooster = null;
    } else {
        state.rewards.dropBooster = parseFloat(value);
    }

    state.currentConfig = null; // Clear current config to hide the booster select
    tempGlobalLevelState.set(stateKey, state);
    await showGlobalLevelCreationInterface(interaction);
}

/**
 * Handle drop booster selection (editing)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected booster value
 */
async function handleGlobalLevelEditDropBoosterSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        state.rewards.dropBooster = null;
    } else {
        state.rewards.dropBooster = parseFloat(value);
    }

    state.currentConfig = null; // Clear current config to hide the booster select
    tempGlobalLevelState.set(stateKey, state);
    return await showGlobalLevelEditInterface(interaction);
}

/**
 * Handle stars selection (creation)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected star amount
 */
async function handleGlobalLevelStarsSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        state.rewards.stars = null;
    } else {
        state.rewards.stars = parseInt(value);
    }

    state.currentConfig = null; // Clear current config to hide the stars select
    tempGlobalLevelState.set(stateKey, state);
    await showGlobalLevelCreationInterface(interaction);
}

/**
 * Handle item selection (creation)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected item ID or 'remove'
 */
async function handleGlobalLevelItemsSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_create`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        // Remove all items
        state.rewards.items = [];
    } else {
        // Set selected item as THE reward (replace any previous selection)
        try {
            const { optimizedFindOne } = require('../../utils/database-optimizer.js');
            const item = await optimizedFindOne('custom_items', { _id: new ObjectId(value) });

            if (item) {
                // Replace any existing item with the selected one
                const rewardItem = {
                    itemId: item._id.toString(),
                    itemName: item.name,
                    itemType: item.type,
                    itemRarity: item.rarity,
                    itemEmote: item.emote,
                    itemDescription: item.description
                };

                // Set as the single item reward (replaces previous selection)
                state.rewards.items = [rewardItem];
            }
        } catch (error) {
            console.error('[handleGlobalLevelItemsSelect] Error adding item:', error);
        }
    }

    state.currentConfig = null; // Clear current config to hide the items select
    tempGlobalLevelState.set(stateKey, state);
    return await showGlobalLevelCreationInterface(interaction);
}

/**
 * Handle stars selection (editing)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected star amount
 */
async function handleGlobalLevelEditStarsSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        state.rewards.stars = null;
    } else {
        state.rewards.stars = parseInt(value);
    }

    state.currentConfig = null; // Clear current config to hide the stars select
    tempGlobalLevelState.set(stateKey, state);
    return await showGlobalLevelEditInterface(interaction);
}

/**
 * Handle item selection (editing)
 * @param {Object} interaction - Discord interaction
 * @param {string} value - Selected item ID or 'remove'
 */
async function handleGlobalLevelEditItemsSelect(interaction, value) {
    const stateKey = `${interaction.user.id}_edit`;
    const state = tempGlobalLevelState.get(stateKey);

    if (!state) {
        const container = await buildGlobalLevelsContainer();
        return container;
    }

    if (value === 'remove') {
        // Remove all items
        state.rewards.items = [];
    } else {
        // Add selected item to rewards
        try {
            const { optimizedFindOne } = require('../../utils/database-optimizer.js');
            const item = await optimizedFindOne('custom_items', { _id: new ObjectId(value) });

            if (item) {
                // Replace any existing item with the selected one
                const rewardItem = {
                    itemId: item._id.toString(),
                    itemName: item.name,
                    itemType: item.type,
                    itemRarity: item.rarity,
                    itemEmote: item.emote,
                    itemDescription: item.description
                };

                // Set as the single item reward (replaces previous selection)
                state.rewards.items = [rewardItem];
            }
        } catch (error) {
            console.error('[handleGlobalLevelEditItemsSelect] Error adding item:', error);
        }
    }

    state.currentConfig = null; // Clear current config to hide the items select
    tempGlobalLevelState.set(stateKey, state);
    return await showGlobalLevelEditInterface(interaction);
}

module.exports = {
    handleGlobalLevelConfigSelect,
    handleGlobalLevelEditConfigSelect,
    handleGlobalLevelExpSelect,
    handleGlobalLevelEditExpSelect,
    handleGlobalLevelIconSelect,
    handleGlobalLevelEditIconSelect,
    handleGlobalPrestigeIconSelect,
    handleGlobalLevelEditPrestigeIconSelect,
    handleGlobalLevelXpBoosterSelect,
    handleGlobalLevelEditXpBoosterSelect,
    handleGlobalLevelDropBoosterSelect,
    handleGlobalLevelEditDropBoosterSelect,
    handleGlobalLevelStarsSelect,
    handleGlobalLevelEditStarsSelect,
    handleGlobalLevelItemsSelect,
    handleGlobalLevelEditItemsSelect,
    createGlobalLevel,
    updateGlobalLevel,
    showGlobalLevelNameModal,
    showGlobalLevelEditNameModal
};
