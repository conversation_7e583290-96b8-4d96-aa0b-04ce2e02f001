const { Con<PERSON><PERSON><PERSON><PERSON><PERSON>, TextD<PERSON>playBuilder, Section<PERSON><PERSON>er, ThumbnailBuilder, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { handleUIOperation } = require('../../utils/interactionManager.js');
const { spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

/**
 * Whisper Models Management Command
 * Allows owner to download and manage Whisper AI models
 */

const WHISPER_MODELS = {
    'tiny': { size: '39 MB', description: 'Fastest, lowest quality' },
    'base': { size: '74 MB', description: 'Good balance of speed and quality' },
    'small': { size: '244 MB', description: 'Better quality, slower' },
    'medium': { size: '769 MB', description: 'High quality, much slower' },
    'large': { size: '1550 MB', description: 'Best quality, very slow' }
};

async function buildWhisperModelsContainer() {
    const container = new ContainerBuilder()
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    // Title and description as direct text displays (no section needed)
    const titleDisplay = new TextDisplayBuilder().setContent('# whisper models');
    const descriptionDisplay = new TextDisplayBuilder().setContent('> download and manage whisper AI models');

    // Info text
    const infoText = new TextDisplayBuilder().setContent(
        '> manage whisper AI models for voice transcription\n\n' +
        '**available models:**\n' +
        Object.entries(WHISPER_MODELS).map(([model, info]) =>
            `**${model}**: ${info.size} - ${info.description}`
        ).join('\n') + '\n\n' +
        '**note:** models are downloaded once and cached locally'
    );

    container.addTextDisplayComponents(titleDisplay, descriptionDisplay, infoText);

    return container;
}

async function buildModelSelectMenu() {
    const modelSelect = new StringSelectMenuBuilder()
        .setCustomId('whisper-model-select')
        .setPlaceholder('select model to download')
        .addOptions(
            Object.entries(WHISPER_MODELS).map(([model, info]) => ({
                label: `${model} model`,
                value: model,
                description: `${info.size} - ${info.description}`,
                emoji: '🎤'
            }))
        );

    return new ActionRowBuilder().addComponents(modelSelect);
}

async function downloadWhisperModel(modelName, interaction) {
    // Auto-detect Python command based on platform
    const pythonPath = process.env.PYTHON_PATH || (process.platform === 'win32' ? 'python' : 'python3');
    
    return new Promise((resolve, reject) => {
        console.log(`[whisperModels] 📥 Starting download of ${modelName} model`);
        
        // Use Python to download the model
        const downloadScript = `
import whisper
import sys

try:
    print(f"Downloading {sys.argv[1]} model...")
    model = whisper.load_model("${modelName}")
    print(f"SUCCESS: {sys.argv[1]} model downloaded and cached successfully!")
except Exception as e:
    print(f"ERROR: Error downloading model: {e}")
    sys.exit(1)
`;
        
        const tempScriptPath = path.join(__dirname, '../../temp_whisper_download.py');
        fs.writeFileSync(tempScriptPath, downloadScript);
        
        const process = spawn(pythonPath, [tempScriptPath, modelName], {
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let stdout = '';
        let stderr = '';
        
        process.stdout.on('data', (data) => {
            stdout += data.toString();
            console.log(`[whisperModels] 📝 ${data.toString().trim()}`);
        });
        
        process.stderr.on('data', (data) => {
            stderr += data.toString();
            console.log(`[whisperModels] 📊 Progress: ${data.toString().trim()}`);
        });
        
        process.on('close', async (code) => {
            // Clean up temp script
            try {
                await fs.remove(tempScriptPath);
            } catch (error) {
                console.warn('[whisperModels] Failed to clean up temp script:', error.message);
            }
            
            if (code === 0) {
                console.log(`[whisperModels] ✅ ${modelName} model download completed`);
                resolve({ success: true, output: stdout });
            } else {
                console.error(`[whisperModels] ❌ ${modelName} model download failed with code ${code}`);
                reject(new Error(`Download failed: ${stderr || 'Unknown error'}`));
            }
        });
        
        process.on('error', (error) => {
            console.error(`[whisperModels] ❌ Process error:`, error);
            reject(error);
        });
    });
}

module.exports = {
    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            const container = await buildWhisperModelsContainer();
            const selectMenu = await buildModelSelectMenu();
            
            return [container, selectMenu];
        }, {
            autoDefer: true,
            ephemeral: true,
            fallbackMessage: '❌ There was an error loading the Whisper models interface. Please try again.'
        });
    },

    async select(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            const selectedModel = interaction.values[0];
            const modelInfo = WHISPER_MODELS[selectedModel];
            
            if (!modelInfo) {
                const container = await buildWhisperModelsContainer();
                const errorText = new TextDisplayBuilder().setContent('**status:** ❌ Invalid model selected.');
                container.addTextDisplayComponents(errorText);
                const selectMenu = await buildModelSelectMenu();
                return [container, selectMenu];
            }
            
            // Show download confirmation
            const container = new ContainerBuilder()
                .setAccentColor(OPERATION_COLORS.WARNING);

            const titleDisplay = new TextDisplayBuilder().setContent('# download confirmation');
            const descriptionDisplay = new TextDisplayBuilder().setContent(`> download ${selectedModel} whisper model`);

            container.addTextDisplayComponents(titleDisplay, descriptionDisplay);
            
            const confirmText = new TextDisplayBuilder().setContent(
                `**model:** ${selectedModel}\n` +
                `**size:** ${modelInfo.size}\n` +
                `**description:** ${modelInfo.description}\n\n` +
                `⚠️ **warning:** this will download ${modelInfo.size} of data and may take several minutes depending on your internet connection.\n\n` +
                `the model will be cached locally for future use.`
            );
            container.addTextDisplayComponents(confirmText);
            
            const confirmButton = new ButtonBuilder()
                .setCustomId(`whisper-download-${selectedModel}`)
                .setLabel(`download ${selectedModel} model`)
                .setStyle(ButtonStyle.Success);
            
            const cancelButton = new ButtonBuilder()
                .setCustomId('whisper-download-cancel')
                .setLabel('cancel')
                .setStyle(ButtonStyle.Secondary);
            
            const buttonRow = new ActionRowBuilder().addComponents(confirmButton, cancelButton);
            
            return [container, buttonRow];
        }, {
            autoDefer: false,
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your model selection. Please try again.'
        });
    },

    async buttons(interaction, args) {
        const [action, model] = args;
        
        if (action === 'download' && model) {
            return handleUIOperation(interaction, async (interaction) => {
                const container = new ContainerBuilder()
                    .setAccentColor(OPERATION_COLORS.NEUTRAL);

                const titleDisplay = new TextDisplayBuilder().setContent('# downloading model');
                const descriptionDisplay = new TextDisplayBuilder().setContent(`> downloading ${model} whisper model...`);

                container.addTextDisplayComponents(titleDisplay, descriptionDisplay);
                
                const statusText = new TextDisplayBuilder().setContent(
                    `**status:** 📥 downloading ${model} model...\n\n` +
                    `this may take several minutes. please wait...\n\n` +
                    `check the console for download progress.`
                );
                container.addTextDisplayComponents(statusText);
                
                // Start download in background
                downloadWhisperModel(model, interaction)
                    .then(() => {
                        console.log(`[whisperModels] ✅ ${model} model download completed successfully`);
                    })
                    .catch((error) => {
                        console.error(`[whisperModels] ❌ ${model} model download failed:`, error);
                    });
                
                return [container];
            }, {
                autoDefer: true,
                ephemeral: true,
                fallbackMessage: '❌ There was an error starting the model download. Please try again.'
            });
        } else if (action === 'cancel') {
            return handleUIOperation(interaction, async (interaction) => {
                const container = await buildWhisperModelsContainer();
                const selectMenu = await buildModelSelectMenu();
                const statusText = new TextDisplayBuilder().setContent('**status:** download cancelled.');
                container.addTextDisplayComponents(statusText);
                
                return [container, selectMenu];
            }, {
                autoDefer: false,
                ephemeral: true,
                fallbackMessage: '❌ There was an error cancelling the download. Please try again.'
            });
        }
    }
};
