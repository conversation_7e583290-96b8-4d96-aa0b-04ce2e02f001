/**
 * Owner Backup System - Modular Component
 * Comprehensive database backup utility for local MongoDB
 */

const { ContainerBuilder, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const { optimizedFindOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

// Performance metrics for backup system
const backupMetrics = {
    totalBackups: 0,
    successfulBackups: 0,
    failedBackups: 0,
    lastBackupTime: null,
    lastBackupSize: 0,
    averageBackupTime: 0,
    verboseLogging: process.env.NODE_ENV === 'development'
};

// State management for cascading menus (like items system)
const backupStateCache = new Map();

/**
 * Store backup UI state (for cascading menus)
 */
function storeBackupState(userId, state) {
    backupStateCache.set(userId, { ...state, updatedAt: new Date() });
}

/**
 * Get backup UI state
 */
function getBackupState(userId) {
    return backupStateCache.get(userId) || { currentConfig: null };
}

/**
 * Clear backup UI state
 */
function clearBackupState(userId) {
    backupStateCache.delete(userId);
}

/**
 * Build backup system container
 */
async function buildBackupContainer(client, userId = null) {
    const startTime = Date.now();
    
    try {
        if (backupMetrics.verboseLogging) {
            console.log('[owner-backup] Building backup container...');
        }

        // Get backup configuration and UI state
        const backupConfig = await getBackupConfig();
        const uiState = userId ? getBackupState(userId) : { currentConfig: null };
        
        // Back button section
        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent('# Database Backup System'))
            .setButtonAccessory(backButton);

        // Quote (title is now in the section)
        const quote = new TextDisplayBuilder().setContent('> Automated MongoDB backup utility with Discord integration');

        // Status display
        const statusText = new TextDisplayBuilder().setContent(
            `**enabled:** ${backupConfig.enabled ? 'yes' : 'no'}\n` +
            `**frequency:** ${getFrequencyDisplay(backupConfig.frequency)}\n` +
            `**channel:** ${backupConfig.channelId ? `<#${backupConfig.channelId}>` : 'not set'}\n` +
            `**last backup:** ${getLastBackupDisplay(backupConfig)}`
        );

        // Separator
        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

        // Configuration select menu
        const configOptions = [
            {
                label: backupConfig.enabled ? 'disable' : 'enable',
                value: 'toggle',
                description: `Currently ${backupConfig.enabled ? 'enabled' : 'disabled'}`,
                emoji: backupConfig.enabled ? '🔴' : '🟢'
            },
            {
                label: 'frequency',
                value: 'frequency',
                description: `Currently: ${getFrequencyDisplay(backupConfig.frequency)}`,
                emoji: '⏰'
            },
            {
                label: 'channel',
                value: 'channel',
                description: backupConfig.channelId ? 'Change backup channel' : 'Set backup channel',
                emoji: '📁'
            }
        ];

        const configSelect = new StringSelectMenuBuilder()
            .setCustomId('backup-config')
            .setPlaceholder(uiState.currentConfig ? getConfigOptionLabel(uiState.currentConfig) : 'backup configuration')
            .addOptions(configOptions);

        const configRow = new ActionRowBuilder().addComponents(configSelect);
        const components = [configRow];

        // Add cascading select menus based on UI state
        if (uiState.currentConfig === 'frequency') {
            const frequencySelect = buildFrequencySelect();
            const frequencyRow = new ActionRowBuilder().addComponents(frequencySelect);
            components.push(frequencyRow);
        } else if (uiState.currentConfig === 'channel') {
            const channelSelect = buildChannelSelect();
            const channelRow = new ActionRowBuilder().addComponents(channelSelect);
            components.push(channelRow);
        }

        // Action buttons (always at the bottom)
        const actionButtons = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('backup-now')
                .setLabel('backup now')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('💾')
                .setDisabled(!backupConfig.enabled || !backupConfig.channelId),
            new ButtonBuilder()
                .setCustomId('backup-stats')
                .setLabel('statistics')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📊')
        );

        // Build container
        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(quote, statusText)
            .addSeparatorComponents(separator)
            .addActionRowComponents(...components, actionButtons)
            .setAccentColor(OPERATION_COLORS.NEUTRAL);

        // Performance tracking
        const duration = Date.now() - startTime;
        if (backupMetrics.verboseLogging || duration > 200) {
            console.log(`[owner-backup] ✅ Backup container built in ${duration}ms`);
        }

        return container;

    } catch (error) {
        console.error('[owner-backup] ❌ Error building backup container:', error);
        throw error;
    }
}

/**
 * Get backup configuration from database
 */
async function getBackupConfig() {
    try {
        const config = await optimizedFindOne('backup_config', { key: 'global' });
        
        return {
            enabled: config?.enabled || false,
            frequency: config?.frequency || 7, // Default: 7 days
            channelId: config?.channelId || null,
            lastBackup: config?.lastBackup || null,
            lastBackupMessageId: config?.lastBackupMessageId || null,
            lastBackupUrl: config?.lastBackupUrl || null
        };
    } catch (error) {
        console.error('[owner-backup] Error getting backup config:', error);
        return {
            enabled: false,
            frequency: 7,
            channelId: null,
            lastBackup: null,
            lastBackupMessageId: null,
            lastBackupUrl: null
        };
    }
}

/**
 * Update backup configuration
 */
async function updateBackupConfig(updates) {
    try {
        await optimizedUpdateOne(
            'backup_config',
            { key: 'global' },
            { $set: { key: 'global', ...updates } },
            { upsert: true }
        );
        
        if (backupMetrics.verboseLogging) {
            console.log('[owner-backup] Config updated:', updates);
        }
    } catch (error) {
        console.error('[owner-backup] Error updating backup config:', error);
        throw error;
    }
}

/**
 * Get frequency display text
 */
function getFrequencyDisplay(frequency) {
    switch (frequency) {
        case 1: return 'daily';
        case 3: return 'every 3 days';
        case 7: return 'weekly';
        default: return `every ${frequency} days`;
    }
}

/**
 * Get config option label for placeholder text
 */
function getConfigOptionLabel(option) {
    switch (option) {
        case 'toggle': return 'enable/disable';
        case 'frequency': return 'frequency';
        case 'channel': return 'channel';
        default: return 'backup configuration';
    }
}

/**
 * Get last backup display text
 */
function getLastBackupDisplay(config) {
    if (!config.lastBackup) {
        return 'never';
    }
    
    const lastBackup = new Date(config.lastBackup);
    const now = new Date();
    const diffMs = now - lastBackup;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    let timeText;
    if (diffDays > 0) {
        timeText = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
        timeText = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
        timeText = 'less than an hour ago';
    }
    
    // Add Discord message link if available
    if (config.lastBackupMessageId && config.channelId) {
        return `${timeText} [view](https://discord.com/channels/@me/${config.channelId}/${config.lastBackupMessageId})`;
    }
    
    return timeText;
}

/**
 * Build frequency selection menu
 */
function buildFrequencySelect() {
    const options = [
        { label: 'daily', value: '1', description: 'Backup every day', emoji: '📅' },
        { label: 'every 3 days', value: '3', description: 'Backup every 3 days', emoji: '📆' },
        { label: 'weekly', value: '7', description: 'Backup every 7 days', emoji: '🗓️' }
    ];

    return new StringSelectMenuBuilder()
        .setCustomId('backup-frequency')
        .setPlaceholder('select backup frequency')
        .addOptions(options);
}

/**
 * Build channel selection menu (using ChannelSelectMenuBuilder)
 */
function buildChannelSelect() {
    const { ChannelSelectMenuBuilder } = require('discord.js');

    return new ChannelSelectMenuBuilder()
        .setCustomId('backup-channel')
        .setPlaceholder('select backup channel')
        .setChannelTypes([0]) // Text channels only
        .setMinValues(1)
        .setMaxValues(1);
}

/**
 * Build statistics container
 */
function buildStatsContainer() {
    const backButton = new ButtonBuilder()
        .setCustomId('backup-back')
        .setLabel('back to backup')
        .setStyle(ButtonStyle.Secondary);

    const backSection = new SectionBuilder()
        .addTextDisplayComponents(new TextDisplayBuilder().setContent('# Backup Statistics'))
        .setButtonAccessory(backButton);

    const quote = new TextDisplayBuilder().setContent('> System performance and backup history');

    const statsText = new TextDisplayBuilder().setContent(
        `**total backups:** ${backupMetrics.totalBackups}\n` +
        `**successful:** ${backupMetrics.successfulBackups}\n` +
        `**failed:** ${backupMetrics.failedBackups}\n` +
        `**success rate:** ${backupMetrics.totalBackups > 0 ? Math.round((backupMetrics.successfulBackups / backupMetrics.totalBackups) * 100) : 0}%\n` +
        `**average time:** ${backupMetrics.averageBackupTime > 0 ? `${Math.round(backupMetrics.averageBackupTime / 1000)}s` : 'N/A'}\n` +
        `**last backup size:** ${backupMetrics.lastBackupSize > 0 ? formatFileSize(backupMetrics.lastBackupSize) : 'N/A'}`
    );

    return new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(quote, statsText)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Perform MongoDB backup
 */
async function performBackup(client, channelId, manual = false) {
    const startTime = Date.now();

    try {
        if (backupMetrics.verboseLogging) {
            console.log('[owner-backup] Starting backup process...');
        }

        // Create backup directory if it doesn't exist
        const backupDir = path.join(process.cwd(), 'backups');
        try {
            await fs.access(backupDir);
        } catch {
            await fs.mkdir(backupDir, { recursive: true });
        }

        // Generate backup filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = path.join(backupDir, `seventeen_bot_backup_${timestamp}.gz`);

        // Get MongoDB connection URI
        const mongoUri = process.env.MONGO_URI;
        if (!mongoUri) {
            throw new Error('MONGO_URI environment variable not set');
        }

        // Perform MongoDB dump
        await new Promise((resolve, reject) => {
            const mongodump = spawn('mongodump', [
                '--uri', mongoUri,
                '--archive', backupFile,
                '--gzip'
            ]);

            mongodump.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`mongodump exited with code ${code}`));
                }
            });

            mongodump.on('error', (error) => {
                reject(new Error(`mongodump error: ${error.message}`));
            });
        });

        // Get file size
        const stats = await fs.stat(backupFile);
        const fileSize = stats.size;

        // Upload to Discord
        const channel = await client.channels.fetch(channelId);
        if (!channel) {
            throw new Error('Backup channel not found');
        }

        const backupType = manual ? 'Manual' : 'Scheduled';
        const message = await channel.send({
            content: `🔄 **${backupType} Database Backup**\n` +
                    `📅 **Date:** ${new Date().toLocaleString()}\n` +
                    `📊 **Size:** ${formatFileSize(fileSize)}\n` +
                    `⏱️ **Duration:** ${Math.round((Date.now() - startTime) / 1000)}s`,
            files: [{
                attachment: backupFile,
                name: `seventeen_bot_backup_${timestamp}.gz`
            }]
        });

        // Update backup config
        await updateBackupConfig({
            lastBackup: new Date(),
            lastBackupMessageId: message.id,
            lastBackupUrl: message.url
        });

        // Update metrics
        backupMetrics.totalBackups++;
        backupMetrics.successfulBackups++;
        backupMetrics.lastBackupTime = Date.now();
        backupMetrics.lastBackupSize = fileSize;

        // Update average backup time
        const duration = Date.now() - startTime;
        if (backupMetrics.averageBackupTime === 0) {
            backupMetrics.averageBackupTime = duration;
        } else {
            backupMetrics.averageBackupTime = (backupMetrics.averageBackupTime + duration) / 2;
        }

        // Clean up local file
        try {
            await fs.unlink(backupFile);
        } catch (error) {
            console.warn('[owner-backup] Warning: Could not delete local backup file:', error.message);
        }

        if (backupMetrics.verboseLogging) {
            console.log(`[owner-backup] ✅ Backup completed successfully in ${Math.round(duration / 1000)}s`);
        }

        return {
            success: true,
            messageId: message.id,
            messageUrl: message.url,
            fileSize: fileSize,
            duration: duration
        };

    } catch (error) {
        console.error('[owner-backup] ❌ Backup failed:', error);

        // Update metrics
        backupMetrics.totalBackups++;
        backupMetrics.failedBackups++;

        throw error;
    }
}

/**
 * Check if backup is due
 */
async function isBackupDue() {
    try {
        const config = await getBackupConfig();

        if (!config.enabled || !config.channelId) {
            return false;
        }

        if (!config.lastBackup) {
            return true; // No backup yet
        }

        const lastBackup = new Date(config.lastBackup);
        const now = new Date();
        const diffMs = now - lastBackup;
        const diffDays = diffMs / (1000 * 60 * 60 * 24);

        return diffDays >= config.frequency;
    } catch (error) {
        console.error('[owner-backup] Error checking backup due:', error);
        return false;
    }
}

/**
 * Initialize backup scheduler
 */
function initializeBackupScheduler(client) {
    // Check for due backups every hour
    setInterval(async () => {
        try {
            if (await isBackupDue()) {
                const config = await getBackupConfig();
                if (config.enabled && config.channelId) {
                    if (backupMetrics.verboseLogging) {
                        console.log('[owner-backup] Scheduled backup starting...');
                    }
                    await performBackup(client, config.channelId, false);
                }
            }
        } catch (error) {
            console.error('[owner-backup] Scheduled backup failed:', error);
        }
    }, 60 * 60 * 1000); // Check every hour

    if (backupMetrics.verboseLogging) {
        console.log('[owner-backup] Backup scheduler initialized');
    }
}

module.exports = {
    buildBackupContainer,
    getBackupConfig,
    updateBackupConfig,
    buildFrequencySelect,
    buildChannelSelect,
    buildStatsContainer,
    performBackup,
    isBackupDue,
    initializeBackupScheduler,
    storeBackupState,
    clearBackupState,
    backupMetrics
};
