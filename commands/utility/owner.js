const { Con<PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { buildSelectMenu } = require('./featuresMenu');
// Import modular owner components
const { buildServersContainer } = require('./owner-servers.js');
const { buildStatusContainer } = require('./owner-status.js');
const { buildGlobalLevelsContainer, handleGlobalLevelsAction } = require('./owner-global-levels.js');
const { buildBackupContainer } = require('./owner-backup.js');
const { handleGlobalLevelConfigSelect, handleGlobalLevelEditConfigSelect, handleGlobalLevelExpSelect, handleGlobalLevelEditExpSelect, handleGlobalLevelIconSelect, handleGlobalLevelEditIconSelect, handleGlobalPrestigeIconSelect, handleGlobalLevelEditPrestigeIconSelect, handleGlobalLevelXpBoosterSelect, handleGlobalLevelEditXpBoosterSelect, handleGlobalLevelDropBoosterSelect, handleGlobalLevelEditDropBoosterSelect, handleGlobalLevelStarsSelect, handleGlobalLevelEditStarsSelect, handleGlobalLevelItemsSelect, handleGlobalLevelEditItemsSelect, createGlobalLevel, updateGlobalLevel } = require('./owner-global-levels-handlers.js');
const { getJoinNotificationConfig, setJoinNotificationConfig, buildJoinNotificationPreview, buildJoinNotificationSetupContainer } = require('./owner-join-notification.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { handleUIOperation } = require('../../utils/interactionManager.js');

async function buildOwnerContainer(client, user) {
    if (!user || user.id !== process.env.OWNER) {
        return new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('who r u? no.')
            )
            .setAccentColor(OPERATION_COLORS.NEUTRAL);
    }
    const heading = new TextDisplayBuilder().setContent('# owner');
    const info = new TextDisplayBuilder().setContent('> lol hey you');

    // Use only slash command mentions (context commands can't be linked)
    const syncText = new TextDisplayBuilder().setContent(`**cmd(s):** ${client._slashCommandMentions || ''}`);

    // Fetch last reload timestamp from DB
    let reloadText = '**reload:** never';
    let syncStatusText = '**synced:** never';
    try {
        const reloadDoc = await optimizedFindOne("stats", { type: "last_reload" });
        if (reloadDoc && reloadDoc.timestamp) {
            reloadText = `**reload:** <t:${reloadDoc.timestamp}:R>`;
        }
        // Fetch last sync status
        const syncDoc = await optimizedFindOne("stats", { type: "last_sync" });
        if (syncDoc && syncDoc.timestamp) {
            syncStatusText = `**synced:** <t:${syncDoc.timestamp}:R>`;
        }
    } catch (e) {
        // fallback: do nothing
    }
    const reloadDisplay = new TextDisplayBuilder().setContent(reloadText);
    const syncStatusDisplay = new TextDisplayBuilder().setContent(syncStatusText);

    // Features select menu
    const featuresSelect = new StringSelectMenuBuilder()
        .setCustomId('owner-features')
        .setPlaceholder('features')
        .addOptions(
            { label: 'servers', value: 'servers', description: 'list of servers the bot is in' },
            { label: 'join notification', value: 'join_notification', description: 'setup join notification' },
            { label: 'status', value: 'status', description: 'update bot status and presence' },
            { label: 'items', value: 'items', description: 'manage bot-wide custom items' },
            { label: 'global levels', value: 'global_levels', description: 'manage global levels system', emoji: '🌟' },
            { label: 'backup', value: 'backup', description: 'database backup system', emoji: '💾' },
            { label: 'whisper models', value: 'whisper_models', description: 'download and manage whisper AI models', emoji: '🎤' },

            { label: 'clear data', value: 'clear_data', description: '⚠️ reset all guild data for fresh start' }
        );
    const featuresRow = new ActionRowBuilder().addComponents(featuresSelect);

    const syncButton = new ButtonBuilder()
        .setCustomId('owner-sync')
        .setLabel('sync')
        .setStyle(ButtonStyle.Secondary);

    const reloadButton = new ButtonBuilder()
        .setCustomId('owner-reload')
        .setLabel('reload')
        .setStyle(ButtonStyle.Secondary);

    const backupButton = new ButtonBuilder()
        .setCustomId('owner-backup-main')
        .setLabel('backup')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('💾');

    const buttonRow = new ActionRowBuilder().addComponents(syncButton, reloadButton, backupButton);

    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, info, syncText, syncStatusDisplay, reloadDisplay)
        .addSeparatorComponents(separator)
        .addActionRowComponents(featuresRow)
        .addActionRowComponents(buttonRow)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    return container;
}

// Old buildServersContainer function removed - now using modular owner-servers.js

// Join notification constants moved to owner-join-notification.js

// --- ITEM NOTIFICATION FEATURE HELPERS ---
const ITEM_NOTIFICATION_DB_KEY = 'global';
const DEFAULT_ITEM_DM_MESSAGE = '🎁 You found {items}! Server: {server} | From: {location}';

// Join notification functions moved to owner-join-notification.js

// Get item notification config from DB (optimized to use shared connection)
async function getItemNotificationConfig() {
    let doc = await optimizedFindOne('item_notifications', { key: ITEM_NOTIFICATION_DB_KEY });
    if (!doc) {
        doc = { key: ITEM_NOTIFICATION_DB_KEY, enabled: true, dmMessage: DEFAULT_ITEM_DM_MESSAGE };
        await optimizedInsertOne('item_notifications', doc);
    }
    return doc;
}

// Set item notification config in DB (optimized to use shared connection)
async function setItemNotificationConfig({ enabled, dmMessage }) {
    await optimizedUpdateOne('item_notifications',
        { key: ITEM_NOTIFICATION_DB_KEY },
        { $set: { enabled, dmMessage } },
        { upsert: true }
    );
}

// sendJoinNotification function moved to owner-join-notification.js

// buildJoinNotificationPreview function moved to owner-join-notification.js

// Build the item notification preview container
function buildItemNotificationPreview({ dmMessage, enabled, botUser, guildName = null }) {
    // Replace placeholders for preview
    let processedMessage = dmMessage;
    if (guildName) {
        processedMessage = processedMessage
            .replace(/{server}/g, guildName)
            .replace(/{items}/g, '🐟 **Rare Fish**')
            .replace(/{location}/g, 'Text Chat');
    }

    // ContainerBuilder and TextDisplayBuilder already imported at top
    const heading = new TextDisplayBuilder().setContent('## item drop notification');
    const quote = new TextDisplayBuilder().setContent(`> ${processedMessage}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, quote)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

// buildJoinNotificationSetupContainer function moved to owner-join-notification.js

// Build the item notification setup container (with back button, static quote, separator, and select menu inside)
function buildItemNotificationSetupContainer({ dmMessage, enabled }, interaction) {
    // Section with back button (like buildServersContainer)
    const heading = new TextDisplayBuilder().setContent('# item notifications');
    const backButton = new ButtonBuilder()
        .setCustomId('owner-itemnotification-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);
    // Static description of the feature
    const quote = new TextDisplayBuilder().setContent('> configure item drop DM messages');
    const status = new TextDisplayBuilder().setContent(`**send DMs:** ${enabled ? 'enabled' : 'disabled'}`);
    // Separator
    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
    // Select menu for item notification config (inside container)
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('owner-itemnotification')
        .setPlaceholder('item notifications')
        .addOptions([
            {
                label: enabled ? 'disable' : 'enable',
                value: 'toggle',
                description: enabled ? 'Currently enabled' : 'Currently disabled',
                default: false
            },
            {
                label: 'DM message',
                value: 'edit',
                description: 'Edit the item drop DM message template',
                default: false
            }
        ]);
    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    // Build container
    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(quote, status)
        .addSeparatorComponents(separator)
        .addActionRowComponents(selectRow)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // This command is not meant to be used as a slash command directly
            // Build basic owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** Owner panel is only accessible via the select menu.');
            container.addTextDisplayComponents(statusDisplay);

            return [container];
        }, {
            autoDefer: true, // Auto-defer for execute function as it may be slow
            ephemeral: true,
            fallbackMessage: '❌ Something went wrong loading the owner interface. Please try again.'
        });
    },
    buildOwnerContainer,
    async select(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            const selectedValue = interaction.values[0];

        // Handle global levels actions
        if (interaction.customId === 'global-levels-action') {
            const result = await handleGlobalLevelsAction(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }

        // Handle global level creation
        if (interaction.customId === 'global-level-config-select') {
            const result = await handleGlobalLevelConfigSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-exp-select') {
            const result = await handleGlobalLevelExpSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-icon-select') {
            const result = await handleGlobalLevelIconSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-prestige-icon-select') {
            await handleGlobalPrestigeIconSelect(interaction, selectedValue);
            return;
        }

        if (interaction.customId === 'global-level-edit-config-select') {
            const result = await handleGlobalLevelEditConfigSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-edit-exp-select') {
            const result = await handleGlobalLevelEditExpSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-edit-icon-select') {
            const result = await handleGlobalLevelEditIconSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-edit-prestige-icon-select') {
            const result = await handleGlobalLevelEditPrestigeIconSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-xp-booster-select') {
            const result = await handleGlobalLevelXpBoosterSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-edit-xp-booster-select') {
            const result = await handleGlobalLevelEditXpBoosterSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-drop-booster-select') {
            const result = await handleGlobalLevelDropBoosterSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-edit-drop-booster-select') {
            const result = await handleGlobalLevelEditDropBoosterSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-stars-select') {
            const result = await handleGlobalLevelStarsSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-items-select') {
            const result = await handleGlobalLevelItemsSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-edit-stars-select') {
            const result = await handleGlobalLevelEditStarsSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }
        if (interaction.customId === 'global-level-edit-items-select') {
            const result = await handleGlobalLevelEditItemsSelect(interaction, selectedValue);
            return Array.isArray(result) ? result : [result];
        }

        if (selectedValue === 'servers') {
            const container = await buildServersContainer(interaction.client);
            return [container];
        } else if (selectedValue === 'join_notification') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }
            const config = await getJoinNotificationConfig();
            const setupContainer = buildJoinNotificationSetupContainer(config, interaction);
            const preview = buildJoinNotificationPreview({
                message: config.message,
                enabled: config.enabled,
                botUser: interaction.client.user,
                guildName: interaction.guild.name
            });
            return [setupContainer, preview];

        } else if (selectedValue === 'status') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }
            const container = await buildStatusContainer(interaction);
            return [container];
        } else if (selectedValue === 'items') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }
            const items = require('./items.js');
            const container = await items.buildItemsContainer({
                isOwner: true,
                guildId: null, // Bot-wide items (not guild-specific)
                page: 0,
                client: interaction.client,
                member: null, // No member context for bot-wide items
                showBackButton: true // Show back button in owner panel context
            });

            return Array.isArray(container) ? container : [container];

        } else if (selectedValue === 'global_levels') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }
            const container = await buildGlobalLevelsContainer(interaction.client, interaction.user);
            return [container];

        } else if (selectedValue === 'backup') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }
            const container = await buildBackupContainer(interaction.client, interaction.user.id);
            return [container];

        } else if (selectedValue === 'whisper_models') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }
            const whisperModels = require('./whisperModels.js');
            return await whisperModels.execute(interaction);
        } else if (selectedValue === 'clear_data') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }
            const clearData = require('./clearData.js');
            await clearData.execute(interaction);
            return;
        }
        // Main owner container: show featuresMenu
        const container = await buildOwnerContainer(interaction.client, interaction.user);
        const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
        return [selectMenu, container];
        }, {
            autoDefer: false, // Don't auto-defer for select menus - they should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your selection. Please try again.'
        });
    },
    async buttons(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.customId === 'owner-back') {
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            return [selectMenu, container];

        } else if (interaction.customId === 'global-level-create-final') {
            const result = await createGlobalLevel(interaction);
            return Array.isArray(result) ? result : [result];
        } else if (interaction.customId === 'global-level-update-final') {
            const result = await updateGlobalLevel(interaction);
            return Array.isArray(result) ? result : [result];
        } else if (interaction.customId === 'global-levels-back') {
            const container = await buildGlobalLevelsContainer();
            return [container];
        } else if (interaction.customId === 'owner-backup-main') {
            // Navigate to backup system from main owner page
            const container = await buildBackupContainer(interaction.client, interaction.user.id);
            return [container];
        } else if (interaction.customId === 'backup-now') {
            // Perform manual backup
            const { performBackup, getBackupConfig } = require('./owner-backup.js');
            const config = await getBackupConfig();

            if (!config.enabled || !config.channelId) {
                const container = await buildBackupContainer(interaction.client, interaction.user.id);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** Backup system not configured properly');
                container.addTextDisplayComponents(statusDisplay);
                return [container];
            }

            try {
                const result = await performBackup(interaction.client, config.channelId, true);
                const container = await buildBackupContainer(interaction.client, interaction.user.id);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** Manual backup completed successfully');
                container.addTextDisplayComponents(statusDisplay);
                return [container];
            } catch (error) {
                const container = await buildBackupContainer(interaction.client, interaction.user.id);
                const statusDisplay = new TextDisplayBuilder().setContent(`**status:** Backup failed: ${error.message}`);
                container.addTextDisplayComponents(statusDisplay);
                return [container];
            }
        } else if (interaction.customId === 'backup-stats') {
            // Show backup statistics
            const { buildStatsContainer } = require('./owner-backup.js');
            const container = buildStatsContainer();
            return [container];
        } else if (interaction.customId === 'backup-back') {
            // Return to backup main page from stats
            const container = await buildBackupContainer(interaction.client, interaction.user.id);
            return [container];
        }
        // ...other button handlers
        }, {
            autoDefer: false, // Don't auto-defer for button presses - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your button press. Please try again.'
        });
    },
    async joinNotificationSelect(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            return [selectMenu, container];
        }
        const config = await getJoinNotificationConfig();
        const selected = interaction.values[0];
        if (selected === 'toggle') {
            await setJoinNotificationConfig({ enabled: !config.enabled, message: config.message });
            const newConfig = await getJoinNotificationConfig();
            const setupContainer = buildJoinNotificationSetupContainer(newConfig, interaction);
            const preview = buildJoinNotificationPreview({
                message: newConfig.message,
                enabled: newConfig.enabled,
                botUser: interaction.client.user,
                guildName: interaction.guild.name
            });
            return [setupContainer, preview];
        } else if (selected === 'edit') {
            // Show modal to edit message
            const modal = new ModalBuilder()
                .setCustomId('owner-joinnotification-modal')
                .setTitle('Edit Join Notification Message')
                .addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('message')
                            .setLabel('Join Message')
                            .setPlaceholder('Available placeholders: {serverName}')
                            .setStyle(TextInputStyle.Paragraph)
                            .setValue(config.message)
                    )
                );
            await interaction.showModal(modal);
            return []; // Modal shown, no components to return
        }
        }, {
            autoDefer: false, // Don't auto-defer for select menus - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your selection. Please try again.'
        });
    },
    async joinNotificationModal(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            return [selectMenu, container];
        }
        const message = interaction.fields.getTextInputValue('message');
        const config = await getJoinNotificationConfig();
        await setJoinNotificationConfig({ enabled: config.enabled, message });
        const newConfig = await getJoinNotificationConfig();
        const setupContainer = buildJoinNotificationSetupContainer(newConfig, interaction);
        const preview = buildJoinNotificationPreview({
            message: newConfig.message,
            enabled: newConfig.enabled,
            botUser: interaction.client.user,
            guildName: interaction.guild.name
        });
        return [setupContainer, preview];
        }, {
            autoDefer: false, // Don't auto-defer for modal submissions - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your form submission. Please try again.'
        });
    },
    async itemNotificationSelect(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            return [selectMenu, container];
        }
        const config = await getItemNotificationConfig();
        const selected = interaction.values[0];
        if (selected === 'toggle') {
            await setItemNotificationConfig({ enabled: !config.enabled, dmMessage: config.dmMessage });

            // Return to items interface
            const items = require('./items.js');
            const container = await items.buildItemsContainer({
                isOwner: true,
                guildId: null, // Bot-wide items (not guild-specific)
                page: 0,
                client: interaction.client,
                member: null, // No member context for bot-wide items
                showBackButton: true // Show back button in owner panel context
            });
            return Array.isArray(container) ? container : [container];
        } else if (selected === 'edit') {
            // Show modal to edit message
            const modal = new ModalBuilder()
                .setCustomId('owner-itemnotification-modal')
                .setTitle('Edit Item Drop DM Message')
                .addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('dmMessage')
                            .setLabel('Item Drop DM Message')
                            .setPlaceholder('Available placeholders: {items}, {server}, {location}')
                            .setStyle(TextInputStyle.Paragraph)
                            .setValue(config.dmMessage)
                    )
                );
            await interaction.showModal(modal);
            return []; // Modal shown, no components to return
        }
        }, {
            autoDefer: false, // Don't auto-defer for select menus - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your selection. Please try again.'
        });
    },
    async itemNotificationModal(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            return [selectMenu, container];
        }
        const dmMessage = interaction.fields.getTextInputValue('dmMessage');
        const config = await getItemNotificationConfig();
        await setItemNotificationConfig({ enabled: config.enabled, dmMessage });

        // Return to items interface
        const items = require('./items.js');
        const container = await items.buildItemsContainer({
            isOwner: true,
            guildId: null, // Bot-wide items (not guild-specific)
            page: 0,
            client: interaction.client,
            member: null, // No member context for bot-wide items
            showBackButton: true // Show back button in owner panel context
        });
        return Array.isArray(container) ? container : [container];
        }, {
            autoDefer: false, // Don't auto-defer for modal submissions - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your form submission. Please try again.'
        });
    },
    async backupConfigSelect(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.user.id !== process.env.OWNER) {
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }

            const { getBackupConfig, updateBackupConfig, storeBackupState } = require('./owner-backup.js');
            const selected = interaction.values[0];

            if (selected === 'toggle') {
                const config = await getBackupConfig();
                await updateBackupConfig({ enabled: !config.enabled });
                const container = await buildBackupContainer(interaction.client, interaction.user.id);
                const statusDisplay = new TextDisplayBuilder().setContent(`**status:** Backup ${!config.enabled ? 'enabled' : 'disabled'}`);
                container.addTextDisplayComponents(statusDisplay);
                return [container];
            } else if (selected === 'frequency') {
                // Store state and show cascading frequency select menu
                storeBackupState(interaction.user.id, { currentConfig: 'frequency' });
                const container = await buildBackupContainer(interaction.client, interaction.user.id);
                return [container];
            } else if (selected === 'channel') {
                // Store state and show cascading channel select menu
                storeBackupState(interaction.user.id, { currentConfig: 'channel' });
                const container = await buildBackupContainer(interaction.client, interaction.user.id);
                return [container];
            }
        }, {
            autoDefer: false,
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your backup configuration.'
        });
    },
    async backupFrequencySelect(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.user.id !== process.env.OWNER) {
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }

            const { updateBackupConfig, clearBackupState } = require('./owner-backup.js');
            const frequency = parseInt(interaction.values[0]);
            await updateBackupConfig({ frequency });

            // Clear state and return to main backup container
            clearBackupState(interaction.user.id);
            const container = await buildBackupContainer(interaction.client, interaction.user.id);
            const statusDisplay = new TextDisplayBuilder().setContent(`**status:** Backup frequency updated`);
            container.addTextDisplayComponents(statusDisplay);
            return [container];
        }, {
            autoDefer: false,
            ephemeral: true,
            fallbackMessage: '❌ There was an error updating backup frequency.'
        });
    },
    async backupChannelSelect(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            if (interaction.user.id !== process.env.OWNER) {
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                return [selectMenu, container];
            }

            const { updateBackupConfig, clearBackupState } = require('./owner-backup.js');
            const channelId = interaction.values[0];
            await updateBackupConfig({ channelId });

            // Clear state and return to main backup container
            clearBackupState(interaction.user.id);
            const container = await buildBackupContainer(interaction.client, interaction.user.id);
            const statusDisplay = new TextDisplayBuilder().setContent(`**status:** Backup channel updated`);
            container.addTextDisplayComponents(statusDisplay);
            return [container];
        }, {
            autoDefer: false,
            ephemeral: true,
            fallbackMessage: '❌ There was an error updating backup channel.'
        });
    },
    async modalSubmit(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            // Handle global level modals
        if (interaction.customId === 'global-level-name-modal') {
            const levelName = interaction.fields.getTextInputValue('level-name');
            const stateKey = `${interaction.user.id}_create`;
            const { tempGlobalLevelState } = require('./owner-global-levels.js');
            const state = tempGlobalLevelState.get(stateKey);

            if (state) {
                state.name = levelName;
                state.currentConfig = null; // Clear current config
                tempGlobalLevelState.set(stateKey, state);

                const { showGlobalLevelCreationInterface } = require('./owner-global-levels.js');
                const result = await showGlobalLevelCreationInterface(interaction);
                return Array.isArray(result) ? result : [result];
            } else {
                const { buildGlobalLevelsContainer } = require('./owner-global-levels.js');
                const container = await buildGlobalLevelsContainer();
                return [container];
            }
            return;
        }

        if (interaction.customId === 'global-level-edit-name-modal') {
            const levelName = interaction.fields.getTextInputValue('level-name');
            const stateKey = `${interaction.user.id}_edit`;
            const { tempGlobalLevelState } = require('./owner-global-levels.js');
            const state = tempGlobalLevelState.get(stateKey);

            if (state) {
                state.name = levelName;
                state.currentConfig = null; // Clear current config
                tempGlobalLevelState.set(stateKey, state);

                const { showGlobalLevelEditInterface } = require('./owner-global-levels.js');
                await showGlobalLevelEditInterface(interaction);
            } else {
                const { buildGlobalLevelsContainer } = require('./owner-global-levels.js');
                const container = await buildGlobalLevelsContainer();
                return [container];
            }
            return;
        }

        // Handle other modals...
        if (interaction.customId === 'join-notification-modal') {
            const message = interaction.fields.getTextInputValue('joinMessage');
            const config = await getJoinNotificationConfig();
            await setJoinNotificationConfig({ enabled: config.enabled, message });

            // Return to join notification interface
            const setupContainer = buildJoinNotificationSetupContainer(config, interaction);
            const preview = buildJoinNotificationPreview({
                message: message,
                enabled: config.enabled,
                botUser: interaction.client.user,
                guildName: interaction.guild.name
            });
            return [setupContainer, preview];
        }

        if (interaction.customId === 'item-notification-modal') {
            return await this.itemNotificationModal(interaction);
        }
        }, {
            autoDefer: false, // Don't auto-defer for modal submissions - should be fast
            ephemeral: true,
            fallbackMessage: '❌ There was an error processing your form submission. Please try again.'
        });
    },
    buildItemNotificationSetupContainer,
    buildItemNotificationPreview,
    getItemNotificationConfig,
    setItemNotificationConfig
};