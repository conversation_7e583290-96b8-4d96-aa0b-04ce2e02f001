/**
 * Admin Voice Message Transcription Context Menu
 * Right-click context menu for transcribing voice messages (admin-only)
 * Displays results in '@username said:' format instead of 'you said:'
 */

const {
    ContextMenuCommandBuilder,
    ApplicationCommandType,
    PermissionFlagsBits,
    ContainerBuilder,
    TextDisplayBuilder,
    ThumbnailBuilder,
    SeparatorBuilder,
    SeparatorSpacingSize,
    MessageFlags
} = require('discord.js');

const { handleUIOperation } = require('../../utils/interactionManager.js');
const { processTranscription } = require('../../utils/transcription.js');
const { OPERATION_COLORS, LOG_COLORS } = require('../../utils/colors.js');
const { incrementCommandUsage } = require('../../utils/commandUsage.js');

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('transcribe')
        .setType(ApplicationCommandType.Message)
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator), // Keep admin perms as fallback
    
    async execute(interaction) {
        return handleUIOperation(interaction, async (interaction) => {
            try {
                // OWNER-ONLY CHECK: Restrict to bot owner only for testing
                if (interaction.user.id !== process.env.OWNER) {
                    console.log(`[transcribe-voice] Access denied for user ${interaction.user.tag} (${interaction.user.id}) - Owner only`);
                    const accessDeniedContainer = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder()
                            .setContent('**status:** ❌ transcribe your own voice messages in the </you:1161326973663060030> command.'))
                        .setAccentColor(OPERATION_COLORS.DELETE);
                    return [accessDeniedContainer];
                }

                // Track command usage
                await incrementCommandUsage('transcribe-voice');

                console.log('[transcribe-voice] Context menu command triggered for message:', interaction.targetMessage?.id);

                const targetMessage = interaction.targetMessage;
                const messageAuthor = targetMessage.author;

                // Validate that the message contains voice attachments
                const voiceAttachments = targetMessage.attachments.filter(attachment => 
                    attachment.contentType && attachment.contentType.startsWith('audio/')
                );

                if (voiceAttachments.size === 0) {
                    const errorContainer = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder()
                            .setContent('**status:** ❌ This message does not contain any voice attachments.'))
                        .setAccentColor(OPERATION_COLORS.DELETE);
                    return [errorContainer];
                }

                // Get the first voice attachment (Discord typically only allows one voice message per message)
                const voiceAttachment = voiceAttachments.first();

                // Validate file size (Discord limit is 25MB, but we should be more conservative)
                if (voiceAttachment.size > 25 * 1024 * 1024) {
                    const errorContainer = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder()
                            .setContent('**status:** ❌ Voice message is too large to transcribe (max 25MB).'))
                        .setAccentColor(OPERATION_COLORS.DELETE);
                    return [errorContainer];
                }

                // Start background transcription process
                console.log(`[transcribe-voice] Starting background transcription for attachment: ${voiceAttachment.id}`);

                // Start the background transcription
                startContextMenuBackgroundTranscription(interaction, targetMessage, voiceAttachment, messageAuthor);

                // Return immediate "processing" response
                const processingContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder()
                        .setContent('**status:** 🎤 downloading voice message and starting transcription...'))
                    .setAccentColor(LOG_COLORS.INFO);

                return [processingContainer];

            } catch (error) {
                console.error('[transcribe-voice] Error in context menu command:', error);
                const errorContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder()
                        .setContent('**status:** ❌ An error occurred while transcribing the voice message.'))
                    .setAccentColor(OPERATION_COLORS.DELETE);
                return [errorContainer];
            }
        }, {
            autoDefer: true, // Voice transcription can take time
            ephemeral: true, // Admin-only, keep private
            fallbackMessage: '❌ There was an error transcribing the voice message. Please try again.'
        });
    }
};

/**
 * Start background transcription for context menu
 * @param {Object} interaction - Discord interaction
 * @param {Object} targetMessage - Target message with voice attachment
 * @param {Object} voiceAttachment - Voice attachment to transcribe
 * @param {Object} messageAuthor - Author of the voice message
 */
async function startContextMenuBackgroundTranscription(interaction, targetMessage, voiceAttachment, messageAuthor) {
    try {
        console.log(`[transcribe-voice] 🎤 Starting background transcription for user ${messageAuthor.id}`);

        // Update status to "processing" after 2 seconds
        setTimeout(async () => {
            try {
                const processingContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder()
                        .setContent('**status:** 🔄 transcribing audio with whisper AI...'))
                    .setAccentColor(LOG_COLORS.INFO);

                await interaction.editReply({
                    components: [processingContainer],
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral
                });
            } catch (error) {
                console.error('[transcribe-voice] Failed to update progress:', error);
            }
        }, 2000); // Update after 2 seconds

        // Process the transcription in background using the existing system
        const result = await processTranscription(interaction, targetMessage.id, voiceAttachment.id);

        // Build result container
        if (result.success) {
            console.log(`[transcribe-voice] ✅ Background transcription completed successfully`);

            // Build transcription result container
            const transcriptionContainer = new ContainerBuilder()
                .setAccentColor(LOG_COLORS.SUCCESS);

            // CRITICAL: Use '@username said:' format instead of 'you said:'
            const titleText = `# @${messageAuthor.username} said:`;
            const transcriptionText = `> ${result.text}`;

            transcriptionContainer.addTextDisplayComponents(
                new TextDisplayBuilder().setContent(titleText),
                new TextDisplayBuilder().setContent(transcriptionText)
            );

            // Add separator (small, no divider)
            transcriptionContainer.addSeparatorComponents(
                new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false)
            );

            // Add metadata (duration, language if detected)
            let metadataText = '';
            if (result.duration) {
                // Use normalizeDuration to match Discord's rounding behavior (always round up)
                const { normalizeDuration } = require('../../utils/transcription.js');
                const duration = normalizeDuration(result.duration);
                metadataText += `**duration:** ${duration}s`;
            }
            if (result.language && result.language !== 'unknown') {
                if (metadataText) metadataText += ' | ';
                metadataText += `**language:** ${result.language}`;
            }
            if (metadataText) {
                transcriptionContainer.addTextDisplayComponents(
                    new TextDisplayBuilder().setContent(metadataText)
                );
            }

            // Update with final result
            await interaction.editReply({
                components: [transcriptionContainer],
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral
            });

        } else {
            console.log(`[transcribe-voice] ❌ Background transcription failed: ${result.error}`);

            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder()
                    .setContent(`**status:** ❌ ${result.error || 'Transcription failed. Please try again later.'}`))
                .setAccentColor(OPERATION_COLORS.DELETE);

            await interaction.editReply({
                components: [errorContainer],
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral
            });
        }

    } catch (error) {
        console.error('[transcribe-voice] Background transcription error:', error);

        try {
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder()
                    .setContent('**status:** ❌ An error occurred during transcription. Please try again.'))
                .setAccentColor(OPERATION_COLORS.DELETE);

            await interaction.editReply({
                components: [errorContainer],
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral
            });
        } catch (replyError) {
            console.error('[transcribe-voice] Failed to send error reply:', replyError);
        }
    }
}
