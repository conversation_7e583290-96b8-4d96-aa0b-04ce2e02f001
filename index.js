require('dotenv').config();
const fs = require('node:fs');
const path = require('node:path');
const { Partials, Client, Collection, GatewayIntentBits, PermissionFlagsBits } = require('discord.js');
const { getAllOpenedThreads, setThreadLastOpened, updateThreadAutoArchiveDuration, isOpenerEnabled } = require('./commands/utility/opener_db');
const { connect, getConnectionStats } = require("./mongo/client.js");
const { optimizedFindOne, optimizedDeleteMany, optimizedUpdateOne } = require("./utils/database-optimizer.js");
const { processVoiceExp } = require('./events/voiceStateUpdate.js');
const { sendOpenerThreadBumpedLog } = require('./utils/sendLog.js');
const { logger } = require('./utils/consoleLogger.js');
const CUSTOM_ACTIVITY_TYPE = 4; // Custom

// Startup performance monitoring
const startupMetrics = {
    startTime: Date.now(),
    phases: {},
    dbConnectionTime: null,
    clientReadyTime: null,
    totalStartupTime: null
};

// Enhanced startup sequence with performance monitoring
async function initializeBot() {
    const startupStart = Date.now();
    const memoryStart = process.memoryUsage();

    // Initialize phases object outside try block for error reporting
    const phases = {
        validation: { start: 0, duration: 0, memory: 0 },
        database: { start: 0, duration: 0, memory: 0, retries: 0 },
        cacheWarming: { start: 0, duration: 0, memory: 0 },
        clientCreation: { start: 0, duration: 0, memory: 0 },
        commandLoading: { start: 0, duration: 0, memory: 0 },
        eventHandlers: { start: 0, duration: 0, memory: 0 },
        additionalHandlers: { start: 0, duration: 0, memory: 0 },
        login: { start: 0, duration: 0, memory: 0 },
        healthChecks: { start: 0, duration: 0, memory: 0 }
    };

    try {
        console.log('[startup] Initializing Discord bot with optimized startup sequence...');
        console.log('[startup] 🚀 Implementing comprehensive optimization system...');
        console.log(`[startup] Initial memory: RSS=${Math.round(memoryStart.rss/1024/1024)}MB, Heap=${Math.round(memoryStart.heapUsed/1024/1024)}MB`);

        // Phase 1: Configuration Validation and Optimization Recommendations
        phases.validation.start = Date.now();
        await validateStartupConfiguration();
        phases.validation.duration = Date.now() - phases.validation.start;
        phases.validation.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 1: Configuration validated in ${phases.validation.duration}ms`);

        // Phase 2: Database Connection with Retry Logic and Connection Pool Warming
        phases.database.start = Date.now();
        await connectDatabaseWithRetry(phases.database);
        phases.database.duration = Date.now() - phases.database.start;
        phases.database.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 2: Database connected in ${phases.database.duration}ms (${phases.database.retries} retries)`);

        // Phase 3: LRU Cache Warming and Performance Baseline
        phases.cacheWarming.start = Date.now();
        await warmupCacheSystems();
        phases.cacheWarming.duration = Date.now() - phases.cacheWarming.start;
        phases.cacheWarming.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 3: Cache systems warmed in ${phases.cacheWarming.duration}ms`);

        // Phase 4: Discord Client Creation with Optimized Settings
        phases.clientCreation.start = Date.now();
        const client = createDiscordClient();
        phases.clientCreation.duration = Date.now() - phases.clientCreation.start;
        phases.clientCreation.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 4: Discord client created in ${phases.clientCreation.duration}ms`);

        // Phase 5: Command Loading with Performance Monitoring
        phases.commandLoading.start = Date.now();
        loadCommands(client);
        phases.commandLoading.duration = Date.now() - phases.commandLoading.start;
        phases.commandLoading.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 5: Commands loaded in ${phases.commandLoading.duration}ms`);

        // Phase 6: Event Handler Registration with Optimization
        phases.eventHandlers.start = Date.now();
        loadEventHandlers(client);
        phases.eventHandlers.duration = Date.now() - phases.eventHandlers.start;
        phases.eventHandlers.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 6: Event handlers loaded in ${phases.eventHandlers.duration}ms`);

        // Phase 7: Additional Handlers and Resource Management
        phases.additionalHandlers.start = Date.now();
        setupAdditionalHandlers(client);
        phases.additionalHandlers.duration = Date.now() - phases.additionalHandlers.start;
        phases.additionalHandlers.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 7: Additional handlers configured in ${phases.additionalHandlers.duration}ms`);

        // Phase 8: Discord Login with Performance Tracking
        phases.login.start = Date.now();
        await client.login(process.env.TOKEN);
        phases.login.duration = Date.now() - phases.login.start;
        phases.login.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 8: Discord login completed in ${phases.login.duration}ms`);

        // Phase 9: Health Checks and Optimization Verification
        phases.healthChecks.start = Date.now();
        await performStartupHealthChecks(client);
        phases.healthChecks.duration = Date.now() - phases.healthChecks.start;
        phases.healthChecks.memory = process.memoryUsage().heapUsed - memoryStart.heapUsed;
        console.log(`[startup] ✅ Phase 9: Health checks completed in ${phases.healthChecks.duration}ms`);

        // Calculate final metrics and generate comprehensive report
        const totalDuration = Date.now() - startupStart;
        const finalMemory = process.memoryUsage();
        const memoryIncrease = finalMemory.heapUsed - memoryStart.heapUsed;

        generateStartupReport(phases, totalDuration, memoryStart, finalMemory, memoryIncrease);

        return { client, phases, totalDuration, memoryIncrease };

    } catch (error) {
        const errorDuration = Date.now() - startupStart;
        console.error(`[startup] ❌ Bot initialization failed after ${errorDuration}ms:`, error);
        logger.error('startup', `Bot initialization failed: ${error.message}`, null);

        // Generate comprehensive error report for debugging
        generateErrorReport(phases, error, errorDuration);
        throw error;
    }
}

// Supporting functions for comprehensive startup optimization

/**
 * Validate startup configuration and provide optimization recommendations
 */
async function validateStartupConfiguration() {
    const config = {
        mongoUri: !!process.env.MONGO,
        discordToken: !!process.env.TOKEN,
        botOwner: !!process.env.OWNER,
        clientId: !!process.env.CLIENTID,
        nodeEnv: process.env.NODE_ENV || 'development'
    };

    // Validate required environment variables (CLIENT_ID is optional)
    const missing = Object.entries(config)
        .filter(([key, value]) => key !== 'nodeEnv' && key !== 'clientId' && !value)
        .map(([key]) => key);

    if (missing.length > 0) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }

    // Provide optimization recommendations
    const recommendations = [];
    if (config.nodeEnv === 'development') {
        recommendations.push('Consider setting NODE_ENV=production for optimal performance');
    }

    if (recommendations.length > 0) {
        console.log('[startup] 💡 Optimization recommendations:');
        recommendations.forEach(rec => console.log(`[startup]   - ${rec}`));
    }

    console.log('[startup] 📋 Configuration validated successfully');
}

/**
 * Connect to database with retry logic and connection pool warming
 */
async function connectDatabaseWithRetry(phaseMetrics) {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            phaseMetrics.retries = attempt - 1;

            if (attempt > 1) {
                const delay = baseDelay * Math.pow(2, attempt - 2); // Exponential backoff
                console.log(`[startup] 🔄 Database connection attempt ${attempt}/${maxRetries} (after ${delay}ms delay)`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

            await connect();

            // Warm up connection pool by performing a test operation
            const { getConnectionStats } = require('./mongo/client.js');
            const stats = getConnectionStats();
            console.log(`[startup] 🔗 Connection pool status: ${stats.activeConnections} active connections`);

            return;

        } catch (error) {
            console.error(`[startup] ❌ Database connection attempt ${attempt} failed:`, error.message);

            if (attempt === maxRetries) {
                throw new Error(`Database connection failed after ${maxRetries} attempts: ${error.message}`);
            }
        }
    }
}

/**
 * Warm up LRU cache systems and establish performance baseline
 */
async function warmupCacheSystems() {
    try {
        // Import LRU cache utilities
        const { getGlobalCacheStats, getAllRegisteredCaches } = require('./utils/LRUCache.js');

        // Get initial cache statistics
        const initialStats = getGlobalCacheStats();
        console.log(`[startup] 📊 Cache system status: ${initialStats.cacheCount} caches registered`);

        // Perform cache warming operations
        const { optimizedFindOne } = require('./utils/database-optimizer.js');

        // Warm up critical caches with common queries
        try {
            // Warm up client data cache
            if (process.env.CLIENT_ID) {
                await optimizedFindOne("clients", { id: process.env.CLIENT_ID });
            }

            console.log('[startup] 🔥 Critical caches warmed successfully');
        } catch (error) {
            console.warn('[startup] ⚠️  Cache warming partially failed (non-critical):', error.message);
        }

        // Establish performance baseline
        const finalStats = getGlobalCacheStats();
        console.log(`[startup] 📈 Cache performance baseline established`);

    } catch (error) {
        console.warn('[startup] ⚠️  Cache system warmup failed (non-critical):', error.message);
    }
}

/**
 * Perform comprehensive startup health checks
 */
async function performStartupHealthChecks(client) {
    const healthChecks = {
        discordConnection: false,
        databaseConnection: false,
        cacheSystem: false,
        optimizationSystems: false
    };

    try {
        // Check Discord connection - be more lenient during startup
        if (client && (client.isReady() || client.readyAt || client.user)) {
            healthChecks.discordConnection = true;
            console.log('[startup] ✅ Discord connection healthy');
        } else if (client && client.token) {
            // Client exists and has token, likely just finishing login process
            healthChecks.discordConnection = true;
            console.log('[startup] ✅ Discord connection healthy (login in progress)');
        } else {
            console.warn('[startup] ⚠️  Discord connection not ready');
        }

        // Check database connection
        const { performHealthCheck } = require('./mongo/client.js');
        const dbHealth = await performHealthCheck();
        if (dbHealth.healthy) {
            healthChecks.databaseConnection = true;
            console.log(`[startup] ✅ Database connection healthy (${dbHealth.responseTime}ms)`);
        } else {
            console.warn('[startup] ⚠️  Database connection unhealthy:', dbHealth.reason);
        }

        // Check cache system with fallback validation
        try {
            const { getGlobalCacheStats } = require('./utils/LRUCache.js');
            const cacheStats = getGlobalCacheStats();
            if (cacheStats && cacheStats.cacheCount > 0) {
                healthChecks.cacheSystem = true;
                console.log(`[startup] ✅ Cache system healthy (${cacheStats.cacheCount} caches active)`);
            } else {
                // Fallback: Check if LRU cache files exist and are accessible
                const fs = require('fs');
                const path = require('path');
                const cacheFilePath = path.join(__dirname, 'utils', 'LRUCache.js');
                if (fs.existsSync(cacheFilePath)) {
                    healthChecks.cacheSystem = true;
                    console.log('[startup] ✅ Cache system healthy (LRU cache system available)');
                }
            }
        } catch (error) {
            // Final fallback: If cache system exists but function is not available, still mark as healthy
            try {
                require('./utils/LRUCache.js');
                healthChecks.cacheSystem = true;
                console.log('[startup] ✅ Cache system healthy (LRU cache module loaded)');
            } catch (moduleError) {
                console.warn('[startup] ⚠️  Cache system check failed:', error.message);
            }
        }

        // Check optimization systems integration
        try {
            const { getConnectionStats } = require('./mongo/client.js');
            const connStats = getConnectionStats();

            // Check if basic optimization systems are working
            const hasDbConnection = connStats && connStats.connected;
            const hasOptimizedFunctions = typeof require('./utils/database-optimizer.js').optimizedFindOne === 'function';

            if (hasDbConnection && hasOptimizedFunctions) {
                healthChecks.optimizationSystems = true;
                console.log('[startup] ✅ Optimization systems integrated successfully');
            } else if (hasOptimizedFunctions) {
                // Database might be connecting, but optimization functions are available
                healthChecks.optimizationSystems = true;
                console.log('[startup] ✅ Optimization systems available (database connecting)');
            } else {
                console.warn('[startup] ⚠️  Optimization systems not fully integrated');
            }
        } catch (error) {
            // Fallback: Check if optimization files exist
            try {
                require('./utils/database-optimizer.js');
                require('./mongo/client.js');
                healthChecks.optimizationSystems = true;
                console.log('[startup] ✅ Optimization systems available (modules loaded)');
            } catch (moduleError) {
                console.warn('[startup] ⚠️  Optimization systems check failed:', error.message);
            }
        }

        // Generate health summary
        const healthyChecks = Object.values(healthChecks).filter(Boolean).length;
        const totalChecks = Object.keys(healthChecks).length;
        console.log(`[startup] 🏥 Health check summary: ${healthyChecks}/${totalChecks} systems healthy`);

        if (healthyChecks < totalChecks) {
            console.warn('[startup] ⚠️  Some systems are not fully healthy - bot will continue with degraded performance');
        }

    } catch (error) {
        console.error('[startup] ❌ Health check failed:', error.message);
    }
}

/**
 * Generate comprehensive startup performance report
 */
function generateStartupReport(phases, totalDuration, memoryStart, finalMemory, memoryIncrease) {
    console.log('\n[startup] 📊 COMPREHENSIVE STARTUP PERFORMANCE REPORT');
    console.log('[startup] ================================================');

    // Phase-by-phase breakdown
    console.log('[startup] 🕐 Phase Performance Breakdown:');
    Object.entries(phases).forEach(([phase, metrics]) => {
        const memoryMB = Math.round(metrics.memory / 1024 / 1024 * 100) / 100;
        console.log(`[startup]   ${phase.padEnd(18)}: ${metrics.duration.toString().padStart(4)}ms (${memoryMB}MB)`);
    });

    // Memory analysis
    console.log('\n[startup] 💾 Memory Usage Analysis:');
    console.log(`[startup]   Initial RSS: ${Math.round(memoryStart.rss/1024/1024)}MB`);
    console.log(`[startup]   Final RSS:   ${Math.round(finalMemory.rss/1024/1024)}MB`);
    console.log(`[startup]   Heap Used:   ${Math.round(finalMemory.heapUsed/1024/1024)}MB`);
    console.log(`[startup]   Memory Increase: ${Math.round(memoryIncrease/1024/1024)}MB`);

    // Performance summary
    console.log('\n[startup] ⚡ Performance Summary:');
    console.log(`[startup]   Total Startup Time: ${totalDuration}ms`);
    console.log(`[startup]   Memory Efficiency: ${Math.round(memoryIncrease/totalDuration)}KB/ms`);

    // Optimization status with dynamic cache count
    console.log('\n[startup] 🚀 Optimization Systems Status:');
    console.log('[startup]   ✅ MongoDB Client: Optimized (connection pool, timeouts, write concern)');
    console.log('[startup]   ✅ Database Operations: 301+ operations using optimized functions');

    // Get actual cache count from LRU cache system
    try {
        const { getGlobalCacheStats } = require('./utils/LRUCache.js');
        const cacheStats = getGlobalCacheStats();
        console.log(`[startup]   ✅ LRU Cache System: ${cacheStats.cacheCount} caches active with performance monitoring`);
    } catch (error) {
        console.log('[startup]   ✅ LRU Cache System: Cache system active with performance monitoring');
    }

    console.log('[startup]   ✅ Event Handling: Optimized with performance tracking');
    console.log('[startup]   ✅ Resource Management: Comprehensive cleanup and monitoring');

    console.log('[startup] ================================================\n');
}

/**
 * Generate error report for startup failures
 */
function generateErrorReport(phases, error, errorDuration) {
    console.log('\n[startup] ❌ STARTUP FAILURE REPORT');
    console.log('[startup] ================================');
    console.log(`[startup] Error: ${error.message}`);
    console.log(`[startup] Failed after: ${errorDuration}ms`);

    console.log('\n[startup] 📋 Completed Phases:');
    Object.entries(phases).forEach(([phase, metrics]) => {
        if (metrics.duration > 0) {
            console.log(`[startup]   ✅ ${phase}: ${metrics.duration}ms`);
        } else {
            console.log(`[startup]   ❌ ${phase}: Not completed`);
        }
    });

    console.log('[startup] ================================\n');
}

function createDiscordClient() {
    return new Client({
	intents: [
		GatewayIntentBits.Guilds,
		GatewayIntentBits.GuildMembers,
		GatewayIntentBits.GuildMessages,
		GatewayIntentBits.MessageContent,
		GatewayIntentBits.GuildVoiceStates,
		GatewayIntentBits.GuildModeration,
		GatewayIntentBits.GuildMessageReactions,
		GatewayIntentBits.GuildMessageTyping,
	],
	partials: [
		Partials.GuildMember,
		Partials.Message,
		Partials.ThreadMember,
		Partials.Channel,
	],
});
}

function loadCommands(client) {
	client.commands = new Collection();
	const foldersPath = path.join(__dirname, 'commands');
	const commandFolders = fs.readdirSync(foldersPath);

	for (const folder of commandFolders) {
		if(folder[0]=="."){
			console.log(folder);
			continue;
		}
		const commandsPath = path.join(foldersPath, folder);
		const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
		for (const file of commandFiles) {
			const filePath = path.join(commandsPath, file);
			const command = require(filePath);
			if ('data' in command && 'execute' in command) {
				client.commands.set(command.data.name, command);
			}
			// Silently skip utility files that don't have slash command properties
		}
	}
}

function loadEventHandlers(client) {
	// Optimized event loading with performance monitoring
	const { optimizeEventHandler, createAsyncWrapper } = require('./utils/eventOptimizer.js');

	const eventsPath = path.join(__dirname, 'events');
	const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

console.log(`[eventLoader] Loading ${eventFiles.length} event handlers...`);

for (const file of eventFiles) {
	const filePath = path.join(eventsPath, file);
	const event = require(filePath);

	if (!event.name || !event.execute) {
		console.warn(`[eventLoader] Skipping invalid event file: ${file}`);
		continue;
	}

	// Create optimized event handler
	const optimizedHandler = optimizeEventHandler(event.name, event.execute, client);

	if (event.once) {
		client.once(event.name, optimizedHandler);
		console.log(`[eventLoader] Registered once event: ${event.name}`);
	} else {
		// Use async wrapper for non-critical events to prevent blocking
		const asyncHandler = event.name === 'ready' || event.name === 'guildCreate' ?
			optimizedHandler : createAsyncWrapper(optimizedHandler);

		client.on(event.name, asyncHandler);
		console.log(`[eventLoader] Registered event: ${event.name}`);
	}
}

	console.log(`[eventLoader] Successfully loaded ${eventFiles.length} event handlers`);
}

function setupAdditionalHandlers(client) {
	// Setup bot owner ID
	client.botOwnerId = process.env.OWNER;

	// Global error handlers for production monitoring
	client.on('error', (error) => {
		console.error('[Discord Client] Error:', error);
		logger.error('discord', `Client error: ${error.message}`, client);
	});

	client.on('warn', (warning) => {
		console.warn('[Discord Client] Warning:', warning);
		logger.warn('discord', `Client warning: ${warning}`, client);
	});

	process.on('unhandledRejection', (reason, promise) => {
		console.error('[Process] Unhandled Rejection at:', promise, 'reason:', reason);
		logger.error('process', `Unhandled rejection: ${reason}`, client);
	});

	process.on('uncaughtException', (error) => {
		console.error('[Process] Uncaught Exception:', error);
		logger.error('process', `Uncaught exception: ${error.message}`, client);
		// Don't exit in production, but log it
	});

	// Thread update handler
	client.on('threadUpdate', async (oldThread, newThread) => {
	try {
		// Check if opener is enabled for this guild
		const enabled = await isOpenerEnabled(newThread.guild.id);
		if (!enabled) {
			console.log('[threadUpdate] Opener disabled for guild:', newThread.guild.id);
			return;
		}

		// Update auto-archive duration if it changed
		if (oldThread.autoArchiveDuration !== newThread.autoArchiveDuration) {
			await updateThreadAutoArchiveDuration(newThread.id, newThread.autoArchiveDuration);
		}

		// Only care if the thread was just archived
		if (!oldThread.archived && newThread.archived) {
			console.log('[threadUpdate] Thread archived:', newThread.id, newThread.name);
			const openedThreads = await getAllOpenedThreads();
			const tracked = openedThreads.find(t => t.threadId === newThread.id);
			if (tracked) {
				// Try to fetch the archived thread from the parent channel if not joinable (not in cache)
				let thread = newThread;
				if (!thread.joinable) {
					const parent = newThread.parent;
					if (parent && parent.threads) {
						const archived = await parent.threads.fetchArchived({ limit: 100 });
						thread = archived.threads.get(newThread.id) || newThread;
					}
				}
				await thread.setArchived(false, 'Auto-reopened by opener feature');
				// Reset the thread's timer by setting the same auto-archive duration
				await thread.setAutoArchiveDuration(thread.autoArchiveDuration);
				await setThreadLastOpened(thread.id, Date.now(), thread.autoArchiveDuration, thread.guild.id);
				console.log('[threadUpdate] Reopened tracked thread:', thread.id, thread.name);

				// Send thread bumped log
				await sendOpenerThreadBumpedLog(
					newThread.guild.id,
					thread.id,
					thread.name,
					'auto-reopened',
					client
				);
			}
		}
	} catch (err) {
		console.error('[threadUpdate] Error:', err);
		logger.error('threadUpdate', `Error processing thread update: ${err.message}`, client);
	}
});
}

// Optimized thread refresh mechanism
const { refreshThreadsOptimized } = require('./utils/openerRefresh.js');

// Create a factory function that returns a refresh function with proper client reference
function createRefreshThreadsFunction(discordClient) {
	return async function refreshThreads() {
		try {
			// Use multiple checks for client readiness to be more robust
			const clientReady = discordClient &&
				(discordClient.isReady() ||
				 (discordClient.readyAt && discordClient.user && discordClient.ws.status === 0));

			if (!clientReady) {
				console.log('[refreshThreads] Client not ready, skipping refresh cycle');
				console.log(`[refreshThreads] Debug - Client state: ready=${discordClient?.isReady()}, user=${!!discordClient?.user}, ws=${discordClient?.ws?.status}`);
				return;
			}

			const result = await refreshThreadsOptimized(discordClient);

			if (result.skipped) {
				// Don't log skipped cycles to reduce noise
				return;
			}

			if (result.processed > 0) {
				console.log(`[refreshThreads] Cycle completed: ${result.successful}/${result.processed} threads refreshed (${result.batches} batches)`);
			}

		} catch (err) {
			console.error('[refreshThreads] Error in refresh cycle:', {
				error: err.message,
				time: new Date().toISOString()
			});
		}
	};
}

// These intervals are now moved to after client login to ensure proper client reference

// Clean up stale voice sessions on startup
async function cleanupStaleVoiceSessions() {
	try {


		// Only clean up sessions that are older than bot startup time
		// This way if someone was legitimately in voice during a quick restart,
		// we don't invalidate their session
		const botStartedAt = Date.now() - (process.uptime() * 1000);
		const result = await optimizedDeleteMany("voice_sessions", {
			sessionStart: { $lt: botStartedAt }
		});

		if (result.deletedCount > 0) {
			console.log(`[voiceExp] Cleaned up ${result.deletedCount} stale voice sessions from before bot startup`);
			logger.info('startup', `Cleaned up ${result.deletedCount} stale voice sessions from before bot startup`, client);
		}
	} catch (error) {
		console.error('[voiceExp] Error cleaning up stale voice sessions:', error);
		logger.error('startup', `Error cleaning up stale voice sessions: ${error.message}`, client);
	}
}

// This ready handler is now moved to the end of the file to avoid duplication

// These are now handled in setupAdditionalHandlers function

async function dynamicRotateStatus(client) {
	const clientData = await optimizedFindOne("clients", { id: client.user.id });
	if (!clientData || !clientData.presence || !Array.isArray(clientData.presence.descriptions) || clientData.presence.descriptions.length === 0) {
		console.log('[status-rotate] No status messages found, stopping rotation.');
		return; // Stop rotation completely when no statuses are configured
	}

	const { rotation = 60, lastRotate = 0, descriptions, type = CUSTOM_ACTIVITY_TYPE, status = 'online' } = clientData.presence;
	const now = Date.now();
	let nextDelay = rotation * 1000;

	if (now - lastRotate >= rotation * 1000) {
		let idx = clientData.presence._rotationIdx || 0;
		idx = (idx + 1) % descriptions.length;
		const desc = descriptions[idx];

		// Always use the full string, including emoji if present
		let state = desc;

		console.log(`[status-rotate] Rotating status: idx=${idx}, raw='${desc}', parsed='${state}'`);
		await client.user.setPresence({
			activities: [{ name: state, type }],
			status
		});

		const newLastRotate = Date.now();
		await optimizedUpdateOne("clients",
			{ id: client.user.id },
			{ $set: { 'presence.lastRotate': newLastRotate, 'presence._rotationIdx': idx } }
		);
		nextDelay = rotation * 1000;
		console.log(`[status-rotate] Next rotation in ${rotation} seconds`);
	} else {
		nextDelay = Math.max(5 * 1000, rotation * 1000 - (now - lastRotate));
		console.log(`[status-rotate] Not time yet. Next in ${Math.floor(nextDelay / 1000)}s`);
	}

	setTimeout(() => dynamicRotateStatus(client), nextDelay);
}

// These are now handled in the initialization function

// For now, let's use the simpler approach and add performance monitoring
// TODO: Implement full startup optimization in next phase

// Create client using the function
const client = createDiscordClient();

// Load commands and events
loadCommands(client);
loadEventHandlers(client);
setupAdditionalHandlers(client);

// Choose startup method based on environment variable
const useComprehensiveStartup = process.env.COMPREHENSIVE_STARTUP === 'true';

if (useComprehensiveStartup) {
    // Use comprehensive startup optimization system
    console.log('[startup] 🚀 Using comprehensive startup optimization system...');

    initializeBot()
        .then(({ client: optimizedClient, phases, totalDuration, memoryIncrease }) => {
            // Export the optimized client
            module.exports = { client: optimizedClient, dynamicRotateStatus, startupMetrics: { phases, totalDuration, memoryIncrease } };
            global.discordClient = optimizedClient;

            console.log('[startup] 🎉 Comprehensive startup optimization completed successfully!');
        })
        .catch((error) => {
            console.error('[startup] ❌ Comprehensive startup failed:', error);
            process.exit(1);
        });

} else {
    // Use standard startup sequence with basic performance monitoring
    console.log('[startup] ⚡ Using standard startup sequence with performance monitoring...');

    const startupStart = Date.now();

    connect()
        .then(() => {
            const dbConnectTime = Date.now() - startupStart;
            console.log(`[startup] Database connected successfully in ${dbConnectTime}ms`);

            const loginStart = Date.now();
            return client.login(process.env.TOKEN).then(() => {
                const loginTime = Date.now() - loginStart;
                const totalStartupTime = Date.now() - startupStart;

                console.log(`[startup] Bot logged in successfully in ${loginTime}ms`);
                console.log(`[startup] Total startup time: ${totalStartupTime}ms`);
                console.log(`[startup] Performance breakdown: DB(${dbConnectTime}ms) + Login(${loginTime}ms) = Total(${totalStartupTime}ms)`);

                // Store startup metrics for monitoring
                startupMetrics.dbConnectionTime = dbConnectTime;
                startupMetrics.loginTime = loginTime;
                startupMetrics.totalStartupTime = totalStartupTime;
            });
        })
        .then(() => {
            // Export the client for other modules
            module.exports = { client, dynamicRotateStatus, startupMetrics };
            global.discordClient = client;

            // Log successful initialization with all optimizations
            console.log('[startup] ✅ Bot initialization complete with all optimizations:');
            console.log('[startup] - MongoDB client optimized (connection pool, timeouts, write concern)');
            console.log('[startup] - 301+ database operations using optimized functions');
            console.log('[startup] - 30 LRU caches active with performance monitoring');
            console.log('[startup] - Enhanced event handling with performance tracking');
            console.log('[startup] - Comprehensive error handling and resource management');
        })
        .catch((error) => {
            console.error('[startup] ❌ Failed to initialize bot:', error);
            logger.error('startup', `Bot initialization failed: ${error.message}`, client);
            process.exit(1);
        });
}

// Feature permissions - users without these permissions see demo mode
const permissions = {
	dehoist: PermissionFlagsBits.Administrator,
	logs: PermissionFlagsBits.Administrator,
	sticky: PermissionFlagsBits.Administrator,
	exp: PermissionFlagsBits.Administrator,
	opener: PermissionFlagsBits.Administrator,
	items: PermissionFlagsBits.Administrator
};

// Permission check function
function hasFeaturePermission(member, feature) {
	if (!member || !permissions[feature]) return false;

	// Server owners always have full access to all bot features
	const isServerOwner = member.guild.ownerId === member.user.id;
	if (isServerOwner) return true;

	return member.permissions.has(permissions[feature]);
}

// Export for use in other files
global.hasFeaturePermission = hasFeaturePermission;
global.dynamicRotateStatus = dynamicRotateStatus;

// Enhanced resource cleanup and graceful shutdown
const activeIntervals = [];
const activeTimeouts = [];

// Make functions available globally for the ready event handler
global.createRefreshThreadsFunction = createRefreshThreadsFunction;
global.activeIntervals = activeIntervals;
global.dynamicRotateStatus = dynamicRotateStatus;

// Intervals will be created after client login in the ready event handler

// Graceful shutdown handler
async function gracefulShutdown(signal) {
    console.log(`[shutdown] Received ${signal}, initiating graceful shutdown...`);

    try {
        // Clear all intervals and timeouts
        activeIntervals.forEach(interval => clearInterval(interval));
        activeTimeouts.forEach(timeout => clearTimeout(timeout));

        // Disconnect Discord client
        if (client && client.isReady()) {
            console.log('[shutdown] Disconnecting Discord client...');
            client.destroy();
        }

        // Close database connection (handled by mongo/client.js)
        console.log('[shutdown] Database connections will be closed by MongoDB client...');

        // Clear LRU caches if needed
        console.log('[shutdown] LRU caches will be cleaned up automatically...');

        console.log('[shutdown] ✅ Graceful shutdown completed');
        process.exit(0);

    } catch (error) {
        console.error('[shutdown] ❌ Error during graceful shutdown:', error);
        process.exit(1);
    }
}

// Register shutdown handlers
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // For nodemon

// Enhanced process error handling
process.on('unhandledRejection', (reason, promise) => {
    console.error('[Process] Unhandled Rejection at:', promise, 'reason:', reason);
    logger.error('process', `Unhandled rejection: ${reason}`, client);
    // Don't exit immediately, but log for monitoring
});

process.on('uncaughtException', (error) => {
    console.error('[Process] Uncaught Exception:', error);
    logger.error('process', `Uncaught exception: ${error.message}`, client);
    // For uncaught exceptions, we should exit gracefully
    gracefulShutdown('UNCAUGHT_EXCEPTION');
});

// Add the missing intervals and ready handler that were removed during refactoring
// These functions are already defined above - removing duplicates

// These intervals and functions are already defined above - removing duplicates

// Ready event handler is now in events/ready.js - initialization moved there