/**
 * Universal Interaction Manager
 * Handles all Discord interaction complexity with 100% reliability
 * Prevents timeout/acknowledgment errors across all bot features
 */

const { MessageFlags } = require('discord.js');
const { trackCommandPerformance } = require('./performanceMonitor.js');

// Configuration
const INTERACTION_TIMEOUT = 2800; // 2.8 seconds - safe buffer before Discord's 3s limit
const DEFER_THRESHOLD = 1000; // Auto-defer operations expected to take >1s

// Metrics and monitoring
const interactionMetrics = {
    totalHandled: 0,
    autoDeferred: 0,
    timeoutsPrevented: 0,
    errorsHandled: 0,
    doubleAcksPrevented: 0,
    fallbacksUsed: 0,
    averageResponseTime: 0,
    verboseLogging: process.env.NODE_ENV === 'development'
};

// Track interaction states to prevent conflicts
const interactionStates = new Map();

/**
 * Universal interaction handler that prevents all timeout/acknowledgment issues
 * @param {Object} interaction - Discord interaction
 * @param {Function} handler - Async function that performs the actual work
 * @param {Object} options - Configuration options
 * @returns {Promise<boolean>} Success status
 */
async function handleInteraction(interaction, handler, options = {}) {
    const startTime = Date.now();
    const interactionId = interaction.id;
    
    // Default options
    const config = {
        autoDefer: true,                    // Automatically defer if operation might take time
        deferThreshold: DEFER_THRESHOLD,    // Threshold for auto-deferring
        ephemeral: true,                    // Default to ephemeral responses
        fallbackMessage: '❌ An error occurred. Please try again.',
        preserveUI: true,                   // Maintain existing UI patterns
        ...options
    };

    try {
        interactionMetrics.totalHandled++;
        
        // Track interaction state
        interactionStates.set(interactionId, {
            startTime,
            deferred: false,
            replied: false,
            status: 'processing'
        });

        // Auto-defer for potentially long operations
        if (config.autoDefer && !interaction.deferred && !interaction.replied) {
            const shouldDefer = await shouldDeferInteraction(interaction, config);
            
            if (shouldDefer) {
                await deferInteractionSafely(interaction, config);
                interactionStates.get(interactionId).deferred = true;
                interactionMetrics.autoDeferred++;
            }
        }

        // Execute the actual handler with timeout protection
        const result = await executeWithTimeoutProtection(interaction, handler, config);
        
        // Update metrics and track performance
        const duration = Date.now() - startTime;
        interactionMetrics.averageResponseTime =
            (interactionMetrics.averageResponseTime + duration) / 2;

        // PERFORMANCE MONITORING: Track command performance for optimization insights
        const commandName = interaction.commandName || interaction.customId?.split('-')[0] || 'unknown';
        trackCommandPerformance(commandName, duration, true);

        if (interactionMetrics.verboseLogging && duration > 500) {
            console.log(`[InteractionManager] ✅ Handled ${interaction.type} interaction in ${duration}ms`);
        } else if (duration < 200) {
            // Log fast operations in development for optimization validation
            if (process.env.NODE_ENV === 'development' && interactionMetrics.verboseLogging) {
                console.log(`[InteractionManager] ⚡ Fast response: ${commandName} in ${duration}ms`);
            }
        }

        return result;

    } catch (error) {
        // PERFORMANCE MONITORING: Track failed operations
        const duration = Date.now() - startTime;
        const commandName = interaction.commandName || interaction.customId?.split('-')[0] || 'unknown';
        trackCommandPerformance(commandName, duration, false);

        return await handleInteractionError(interaction, error, config);
    } finally {
        // Cleanup
        setTimeout(() => {
            interactionStates.delete(interactionId);
        }, 300000); // Clean up after 5 minutes
    }
}

/**
 * Determine if an interaction should be deferred
 */
async function shouldDeferInteraction(interaction, config) {
    // PERFORMANCE OPTIMIZATION: Only defer operations that actually need it

    // Fast slash commands that should NOT be deferred (major performance improvement)
    if (interaction.isChatInputCommand()) {
        const fastCommands = ['17', 'you', 'lookup', 'changelog'];
        const commandName = interaction.commandName;

        // Don't defer fast commands - they should respond in <1s
        if (fastCommands.includes(commandName)) {
            return false;
        }

        // Only defer potentially slow commands
        const slowCommands = ['items', 'exp', 'logs', 'dehoist', 'sticky', 'opener', 'owner'];
        return slowCommands.includes(commandName);
    }

    // Defer select menus for complex operations (items, exp, etc.)
    if (interaction.isStringSelectMenu()) {
        const complexOperations = ['items-config-select', 'items-image-select', 'exp-level-select', 'exp-role-select'];
        return complexOperations.some(op => interaction.customId.includes(op));
    }

    // Defer button interactions for operations that modify data
    if (interaction.isButton()) {
        const dataModifyingButtons = ['create-', 'edit-', 'delete-', 'save-', 'upload-', 'prestige-'];
        return dataModifyingButtons.some(op => interaction.customId.includes(op));
    }

    return false;
}

/**
 * Safely defer an interaction with error handling
 */
async function deferInteractionSafely(interaction, config) {
    try {
        if (interaction.isChatInputCommand()) {
            await interaction.deferReply({
                flags: config.ephemeral ? MessageFlags.Ephemeral : undefined
            });
        } else {
            await interaction.deferUpdate();
        }
        
        if (interactionMetrics.verboseLogging) {
            console.log(`[InteractionManager] 🔄 Auto-deferred ${interaction.type} interaction`);
        }
        
    } catch (error) {
        // If defer fails, the interaction might already be handled
        if (error.code !== 40060) { // Not "already acknowledged"
            console.warn(`[InteractionManager] ⚠️ Failed to defer interaction: ${error.message}`);
        }
    }
}

/**
 * Execute handler with timeout protection
 */
async function executeWithTimeoutProtection(interaction, handler, config) {
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            reject(new Error('INTERACTION_TIMEOUT'));
        }, INTERACTION_TIMEOUT);
    });

    try {
        // Race between handler execution and timeout
        const result = await Promise.race([
            handler(interaction),
            timeoutPromise
        ]);
        
        return result;
        
    } catch (error) {
        if (error.message === 'INTERACTION_TIMEOUT') {
            interactionMetrics.timeoutsPrevented++;
            
            // Try to send a timeout message if possible
            await sendTimeoutMessage(interaction, config);
            return false;
        }
        throw error;
    }
}

/**
 * Send timeout message to user
 */
async function sendTimeoutMessage(interaction, config) {
    try {
        const timeoutMessage = {
            content: '⏰ This operation is taking longer than expected. Please try again.',
            flags: MessageFlags.Ephemeral
        };

        if (interaction.deferred) {
            await interaction.editReply(timeoutMessage);
        } else if (!interaction.replied) {
            await interaction.reply(timeoutMessage);
        }
    } catch (error) {
        // Silently fail - interaction might be expired
    }
}

/**
 * Handle interaction errors with comprehensive fallbacks and state tracking
 * ENHANCED: Incorporates best practices from discordErrorHandler.js
 */
async function handleInteractionError(interaction, error, config) {
    interactionMetrics.errorsHandled++;
    const interactionId = interaction.id;

    // Check if interaction is likely timed out (15 minutes)
    const interactionAge = Date.now() - interaction.createdTimestamp;
    if (interactionAge > 900000) {
        if (interactionMetrics.verboseLogging) {
            console.warn(`[InteractionManager] Skipping timed out interaction (${Math.round(interactionAge/1000)}s old)`);
        }
        return false;
    }

    // Don't log common timeout/acknowledgment errors unless in verbose mode
    if (error.code === 10062 || error.code === 40060) {
        interactionMetrics.doubleAcksPrevented = (interactionMetrics.doubleAcksPrevented || 0) + 1;
        if (interactionMetrics.verboseLogging) {
            console.warn(`[InteractionManager] Prevented double acknowledgment ${error.code}: ${error.message}`);
        }
        return false;
    }

    // Log unexpected errors with context
    console.error(`[InteractionManager] ❌ Unexpected error in ${interaction.customId || 'unknown'}:`, error);

    // Enhanced error response with multiple fallback strategies
    try {
        const errorMessage = {
            content: config.fallbackMessage || '❌ An unexpected error occurred. Please try again.',
            flags: MessageFlags.Ephemeral
        };

        // Primary strategy: Use appropriate method based on interaction state
        if (interaction.deferred && !interaction.replied) {
            await interaction.editReply(errorMessage);
            return true;
        } else if (!interaction.replied && !interaction.deferred) {
            await interaction.reply(errorMessage);
            return true;
        }

        // Fallback strategy: Try followUp if interaction was already replied to
        if (interaction.replied && !interaction.ephemeral) {
            interactionMetrics.fallbacksUsed = (interactionMetrics.fallbacksUsed || 0) + 1;
            await interaction.followUp(errorMessage);
            return true;
        }

    } catch (responseError) {
        // Enhanced error logging for debugging
        if (interactionMetrics.verboseLogging) {
            console.warn(`[InteractionManager] Failed to send error message: ${responseError.message}`);
        }
    }

    return false;
}

/**
 * Create an ephemeral followUp response object
 * Note: For button/select interactions, this will use reply() instead of followUp() automatically
 * @param {Object} options - FollowUp options (content, components, etc.)
 * @returns {Object} Special response object for ephemeral followUps
 */
function createEphemeralFollowUp(options) {
    return {
        _ephemeralFollowUp: true,
        options: options
    };
}

/**
 * Specialized handler for UI operations that need to preserve existing patterns
 */
async function handleUIOperation(interaction, uiBuilder, options = {}) {
    return handleInteraction(interaction, async (interaction) => {
        // Build the UI components
        const components = await uiBuilder(interaction);

        // Check if interaction was already handled (e.g., modal shown)
        if (interaction.replied) {
            // Interaction already handled by the uiBuilder (e.g., modal shown)
            return true;
        }

        // Check for special ephemeral followUp response
        if (components && components._ephemeralFollowUp) {
            // Handle ephemeral followUp response with proper flags
            const { MessageFlags } = require('discord.js');
            const followUpOptions = {
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                ...components.options
            };

            // For button/select interactions that haven't been replied to, use reply instead of followUp
            if (!interaction.replied && !interaction.deferred &&
                (interaction.isButton() || interaction.isStringSelectMenu() || interaction.isAnySelectMenu())) {
                await interaction.reply(followUpOptions);
            } else {
                // For other cases or already replied interactions, use followUp
                await interaction.followUp(followUpOptions);
            }
            return true;
        }

        // If no components returned, assume interaction was handled internally
        if (!components || (Array.isArray(components) && components.length === 0)) {
            return true;
        }

        // Determine response method based on interaction type and state
        const responseOptions = {
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: Array.isArray(components) ? components : [components]
        };

        // Use appropriate response method based on interaction state
        if (interaction.deferred) {
            // If already deferred, use editReply
            await interaction.editReply(responseOptions);
        } else if (interaction.isChatInputCommand() || interaction.isContextMenuCommand()) {
            // For slash commands and context menu commands, use reply
            await interaction.reply(responseOptions);
        } else {
            // For components (buttons, select menus), use update
            await interaction.update(responseOptions);
        }
        
        return true;
    }, options);
}

/**
 * Get interaction manager metrics
 */
function getMetrics() {
    return { ...interactionMetrics };
}

/**
 * Reset metrics (for testing)
 */
function resetMetrics() {
    Object.keys(interactionMetrics).forEach(key => {
        if (typeof interactionMetrics[key] === 'number') {
            interactionMetrics[key] = 0;
        }
    });
}

module.exports = {
    handleInteraction,
    handleUIOperation,
    createEphemeralFollowUp,
    getMetrics,
    resetMetrics
};
