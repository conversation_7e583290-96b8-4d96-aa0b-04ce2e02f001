/**
 * High-Performance LRU Cache Implementation
 * Provides memory-efficient caching with automatic eviction and TTL support
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

class LRUCache {
    constructor(maxSize = 1000, ttl = 300000) { // Default: 1000 items, 5 minutes TTL
        this.maxSize = maxSize;
        this.ttl = ttl;
        this.cache = new Map();
        this.accessOrder = new Map(); // Track access order for LRU
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            sets: 0,
            deletes: 0
        };
    }

    /**
     * Get value from cache
     * @param {string} key - Cache key
     * @returns {*} Cached value or undefined
     */
    get(key) {
        const entry = this.cache.get(key);
        
        if (!entry) {
            this.stats.misses++;
            return undefined;
        }
        
        // Check TTL
        if (Date.now() - entry.timestamp > this.ttl) {
            this.delete(key);
            this.stats.misses++;
            return undefined;
        }
        
        // Update access order (move to end = most recently used)
        this.accessOrder.delete(key);
        this.accessOrder.set(key, Date.now());
        
        this.stats.hits++;
        return entry.value;
    }

    /**
     * Set value in cache
     * @param {string} key - Cache key
     * @param {*} value - Value to cache
     */
    set(key, value) {
        const now = Date.now();
        
        // If key exists, update it
        if (this.cache.has(key)) {
            this.cache.set(key, { value, timestamp: now });
            this.accessOrder.delete(key);
            this.accessOrder.set(key, now);
            this.stats.sets++;
            return;
        }
        
        // Check if we need to evict
        if (this.cache.size >= this.maxSize) {
            this.evictLRU();
        }
        
        // Add new entry
        this.cache.set(key, { value, timestamp: now });
        this.accessOrder.set(key, now);
        this.stats.sets++;
    }

    /**
     * Delete key from cache
     * @param {string} key - Cache key
     * @returns {boolean} True if key existed
     */
    delete(key) {
        const existed = this.cache.delete(key);
        this.accessOrder.delete(key);
        
        if (existed) {
            this.stats.deletes++;
        }
        
        return existed;
    }

    /**
     * Check if key exists in cache (without updating access order)
     * @param {string} key - Cache key
     * @returns {boolean} True if key exists and not expired
     */
    has(key) {
        const entry = this.cache.get(key);
        
        if (!entry) {
            return false;
        }
        
        // Check TTL
        if (Date.now() - entry.timestamp > this.ttl) {
            this.delete(key);
            return false;
        }
        
        return true;
    }

    /**
     * Clear all entries from cache
     */
    clear() {
        this.cache.clear();
        this.accessOrder.clear();
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            sets: 0,
            deletes: 0
        };
    }

    /**
     * Evict least recently used entry
     * @private
     */
    evictLRU() {
        if (this.accessOrder.size === 0) {
            return;
        }
        
        // Get least recently used key (first in accessOrder map)
        const lruKey = this.accessOrder.keys().next().value;
        
        this.cache.delete(lruKey);
        this.accessOrder.delete(lruKey);
        this.stats.evictions++;
        
        console.log(`[LRUCache] Evicted LRU entry: ${lruKey}`);
    }

    /**
     * Clean up expired entries
     * @returns {number} Number of entries cleaned up
     */
    cleanupExpired() {
        const now = Date.now();
        let cleaned = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > this.ttl) {
                this.delete(key);
                cleaned++;
            }
        }
        
        if (cleaned > 0) {
            console.log(`[LRUCache] Cleaned up ${cleaned} expired entries`);
        }
        
        return cleaned;
    }

    /**
     * Get cache statistics
     * @returns {Object} Cache statistics
     */
    getStats() {
        const hitRate = this.stats.hits + this.stats.misses > 0 
            ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
            : 0;
            
        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            hitRate: `${hitRate}%`,
            hits: this.stats.hits,
            misses: this.stats.misses,
            evictions: this.stats.evictions,
            sets: this.stats.sets,
            deletes: this.stats.deletes,
            memoryUsage: this.getMemoryUsage()
        };
    }

    /**
     * Get approximate memory usage
     * @returns {Object} Memory usage information
     */
    getMemoryUsage() {
        // Rough estimation of memory usage
        const avgKeySize = 50; // Average key size in bytes
        const avgValueSize = 200; // Average value size in bytes
        const entryOverhead = 100; // Overhead per entry
        
        const estimatedBytes = this.cache.size * (avgKeySize + avgValueSize + entryOverhead);
        const estimatedMB = (estimatedBytes / 1024 / 1024).toFixed(2);
        
        return {
            estimatedBytes,
            estimatedMB: `${estimatedMB} MB`,
            entries: this.cache.size
        };
    }

    /**
     * Get cache keys sorted by access time (most recent first)
     * @returns {Array} Array of keys
     */
    getKeysByAccessTime() {
        return Array.from(this.accessOrder.keys()).reverse();
    }

    /**
     * Get cache entries that will expire soon
     * @param {number} withinMs - Time window in milliseconds
     * @returns {Array} Array of keys that will expire soon
     */
    getExpiringSoon(withinMs = 60000) { // Default: 1 minute
        const now = Date.now();
        const expiringSoon = [];
        
        for (const [key, entry] of this.cache.entries()) {
            const timeToExpiry = this.ttl - (now - entry.timestamp);
            if (timeToExpiry > 0 && timeToExpiry <= withinMs) {
                expiringSoon.push({
                    key,
                    expiresIn: timeToExpiry
                });
            }
        }
        
        return expiringSoon.sort((a, b) => a.expiresIn - b.expiresIn);
    }
}

/**
 * Create a new LRU cache instance with optimized settings for different use cases
 */
class CacheFactory {
    /**
     * Create cache for user data (OPTIMIZED: larger size, longer TTL for better hit rates)
     */
    static createUserCache() {
        return new LRUCache(5000, 10 * 60 * 1000); // 5000 entries, 10 minutes (was 2000/5min)
    }

    /**
     * Create cache for guild data (OPTIMIZED: larger size, much longer TTL for stable data)
     */
    static createGuildCache() {
        return new LRUCache(2000, 30 * 60 * 1000); // 2000 entries, 30 minutes (was 500/10min)
    }

    /**
     * Create cache for frequently accessed data (OPTIMIZED: larger size, optimized TTL)
     */
    static createHighFrequencyCache() {
        return new LRUCache(10000, 5 * 60 * 1000); // 10000 entries, 5 minutes (was 5000/2min)
    }

    /**
     * Create cache for expensive computations (OPTIMIZED: larger size, longer TTL)
     */
    static createComputationCache() {
        return new LRUCache(3000, 30 * 60 * 1000); // 3000 entries, 30 minutes (was 1000/15min)
    }

    /**
     * Create cache for ultra-fast operations (NEW: for /17, /you commands)
     */
    static createUltraFastCache() {
        return new LRUCache(15000, 15 * 60 * 1000); // 15000 entries, 15 minutes
    }
}

// Global cache cleanup interval (every 5 minutes)
const globalCaches = new Set();

function registerCache(cache) {
    globalCaches.add(cache);
}

function unregisterCache(cache) {
    globalCaches.delete(cache);
}

/**
 * Get global cache statistics for all registered caches
 * @returns {Object} Global cache statistics
 */
function getGlobalCacheStats() {
    let totalHits = 0;
    let totalMisses = 0;
    let totalEvictions = 0;
    let totalSets = 0;
    let totalDeletes = 0;
    let totalMemoryUsage = 0;
    let totalEntries = 0;

    for (const cache of globalCaches) {
        const stats = cache.getStats();
        totalHits += stats.hits;
        totalMisses += stats.misses;
        totalEvictions += stats.evictions;
        totalSets += stats.sets;
        totalDeletes += stats.deletes;
        totalMemoryUsage += stats.memoryUsage;
        totalEntries += stats.size;
    }

    const hitRate = totalHits + totalMisses > 0 ?
        (totalHits / (totalHits + totalMisses) * 100).toFixed(2) : 0;

    return {
        cacheCount: globalCaches.size,
        totalEntries,
        totalMemoryUsage,
        hitRate: `${hitRate}%`,
        totalHits,
        totalMisses,
        totalEvictions,
        totalSets,
        totalDeletes
    };
}

/**
 * Get all registered caches
 * @returns {Set} Set of all registered cache instances
 */
function getAllRegisteredCaches() {
    return new Set(globalCaches);
}

/**
 * Clear all registered caches
 */
function clearAllRegisteredCaches() {
    for (const cache of globalCaches) {
        cache.clear();
    }
    console.log(`[LRUCache] Cleared all ${globalCaches.size} registered caches`);
}

// Cleanup expired entries across all registered caches
setInterval(() => {
    let totalCleaned = 0;
    for (const cache of globalCaches) {
        totalCleaned += cache.cleanupExpired();
    }

    if (totalCleaned > 0) {
        console.log(`[LRUCache] Global cleanup: ${totalCleaned} expired entries across ${globalCaches.size} caches`);
    }
}, 5 * 60 * 1000);

module.exports = {
    LRUCache,
    CacheFactory,
    registerCache,
    unregisterCache,
    getGlobalCacheStats,
    getAllRegisteredCaches,
    clearAllRegisteredCaches
};
