/**
 * Prestige UI Components
 * Handles prestige button interface and confirmation system
 */

const { Con<PERSON>er<PERSON><PERSON>er, SectionBuilder, TextDisplayBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, MessageFlags } = require('discord.js');
const { getCachedGlobalUser, getCachedGlobalLevels, calculateGlobalLevel, performPrestige } = require('./globalLevels.js');
const { LOG_COLORS } = require('./colors.js');
const { handleUIOperation } = require('./interactionManager.js');

// Track prestige confirmation clicks (userId -> clickCount)
const prestigeConfirmationClicks = new Map();

// Reset confirmation clicks after 30 seconds
const CONFIRMATION_TIMEOUT = 30 * 1000;

/**
 * Build prestige button container when user can prestige
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Prestige button container
 */
async function buildPrestigeContainer(userId) {
    try {
        const userData = await getCachedGlobalUser(userId);
        const levels = await getCachedGlobalLevels();
        const levelCalc = calculateGlobalLevel(userData.globalExp, userData.prestigeLevel, levels);
        
        if (!levelCalc.canPrestige) {
            return null; // User cannot prestige
        }
        
        const clickCount = prestigeConfirmationClicks.get(userId) || 0;
        const nextPrestigeLevel = userData.prestigeLevel + 1;
        const nextMultiplier = nextPrestigeLevel + 1; // 2x, 3x, 4x, 5x, 6x
        
        let buttonText, buttonStyle, description;
        
        if (clickCount === 0) {
            buttonText = 'prestige';
            buttonStyle = ButtonStyle.Secondary;
            description = `ready to prestige! click the button 3 times to confirm.\n\n` +
                         `**prestige ${nextPrestigeLevel}:**\n` +
                         `\\- reset to level 0 with ${nextMultiplier}x XP requirements\n` +
                         `\\- keep all items and boosters\n` +
                         `\\- unlock harder progression for ultimate rewards`;
        } else if (clickCount === 1) {
            buttonText = 'prestige (2 more)';
            buttonStyle = ButtonStyle.Primary;
            description = `click 2 more times to confirm prestige.\n\n` +
                         `**warning:** this will reset your global level to 0!\n` +
                         `your items and boosters will be preserved.`;
        } else if (clickCount === 2) {
            buttonText = 'PRESTIGE (1 more)';
            buttonStyle = ButtonStyle.Danger;
            description = `**final confirmation!** click once more to prestige.\n\n` +
                         `this action cannot be undone.`;
        }
        
        const container = new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent(
                    `## prestige available! ⭐\n\n${description}`
                )
            )
            .setAccentColor(LOG_COLORS.PREMIUM);
        
        const prestigeButton = new ButtonBuilder()
            .setCustomId(`prestige-confirm-${userId}`)
            .setLabel(buttonText)
            .setStyle(buttonStyle)
            .setEmoji('⭐');
        
        const cancelButton = new ButtonBuilder()
            .setCustomId(`prestige-cancel-${userId}`)
            .setLabel('cancel')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('❌');
        
        const buttonRow = new ActionRowBuilder().addComponents(prestigeButton, cancelButton);
        
        return { container, buttonRow };
        
    } catch (error) {
        console.error('[prestigeUI] Error building prestige container:', error);
        return null;
    }
}

/**
 * Handle prestige button click
 * @param {Object} interaction - Discord interaction
 * @returns {Promise<boolean>} True if prestige was completed
 */
async function handlePrestigeClick(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        const userId = interaction.user.id;
        const currentClicks = prestigeConfirmationClicks.get(userId) || 0;

        if (currentClicks >= 2) {
            // Third click - perform prestige
            try {
                const result = await performPrestige(userId);

                if (result.success) {
                    // Clear confirmation clicks
                    prestigeConfirmationClicks.delete(userId);

                    const container = new ContainerBuilder()
                        .addTextDisplayComponents(
                            new TextDisplayBuilder().setContent(
                                `## prestige complete! 🌟\n\n` +
                                `congratulations! you have prestiged to **prestige ${result.newPrestigeLevel}**!\n\n` +
                                `**new progression:**\n` +
                                `\\- global level reset to 0\n` +
                                `\\- XP requirements increased by ${result.newPrestigeMultiplier}x\n` +
                                `\\- all items and boosters preserved\n\n` +
                                `start gaining XP to progress through the harder levels!`
                            )
                        )
                        .setAccentColor(LOG_COLORS.SUCCESS);

                    // CONVERTED: Return components instead of calling interaction.update
                    return [container];
                }
            } catch (prestigeError) {
                console.error('[prestigeUI] Error performing prestige:', prestigeError);

                const container = new ContainerBuilder()
                    .addTextDisplayComponents(
                        new TextDisplayBuilder().setContent(
                            `## prestige failed ❌\n\n` +
                            `failed to perform prestige. please try again later.\n\n` +
                            `**error:** ${prestigeError.message}`
                        )
                    )
                    .setAccentColor(LOG_COLORS.ERROR);

                // CONVERTED: Return components instead of calling interaction.update
                return [container];
            }
        } else {
            // First or second click - increment counter and update UI
            const newClickCount = currentClicks + 1;
            prestigeConfirmationClicks.set(userId, newClickCount);

            // Set timeout to reset clicks
            setTimeout(() => {
                if (prestigeConfirmationClicks.get(userId) === newClickCount) {
                    prestigeConfirmationClicks.delete(userId);
                }
            }, CONFIRMATION_TIMEOUT);

            // Rebuild prestige container with updated click count
            const prestigeUI = await buildPrestigeContainer(userId);

            if (prestigeUI) {
                // CONVERTED: Return components instead of calling interaction.update
                return [prestigeUI.container, prestigeUI.buttonRow];
            }

            // Return empty array if no UI to show
            return [];
        }
    }, {
        autoDefer: false, // Don't auto-defer for button presses - should be fast
        ephemeral: true,
        fallbackMessage: '❌ There was an error processing your prestige action. Please try again.'
    });
}

/**
 * Handle prestige cancel button
 * @param {Object} interaction - Discord interaction
 */
async function handlePrestigeCancel(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        const userId = interaction.user.id;

        // Clear confirmation clicks
        prestigeConfirmationClicks.delete(userId);

        const container = new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent(
                    `## prestige cancelled\n\n` +
                    `prestige has been cancelled. you can try again anytime.\n\n` +
                    `your progress remains unchanged.`
                )
            )
            .setAccentColor(LOG_COLORS.WARNING);

        // CONVERTED: Return components instead of calling interaction.update
        return [container];
    }, {
        autoDefer: false, // Don't auto-defer for button presses - should be fast
        ephemeral: true,
        fallbackMessage: '❌ There was an error cancelling prestige. Please try again.'
    });
}

/**
 * Check if user has pending prestige confirmation
 * @param {string} userId - User ID
 * @returns {number} Number of confirmation clicks (0-2)
 */
function getPrestigeConfirmationClicks(userId) {
    return prestigeConfirmationClicks.get(userId) || 0;
}

/**
 * Clear prestige confirmation for a user
 * @param {string} userId - User ID
 */
function clearPrestigeConfirmation(userId) {
    prestigeConfirmationClicks.delete(userId);
}

/**
 * Get prestige display text for level displays
 * @param {number} prestigeLevel - User's prestige level
 * @param {string} prestigeIcon - Prestige icon from level config
 * @returns {string} Prestige display text
 */
function getPrestigeDisplayText(prestigeLevel, prestigeIcon = '⭐') {
    if (prestigeLevel === 0) {
        return '';
    }
    
    return `${prestigeIcon}${prestigeLevel} `;
}

module.exports = {
    buildPrestigeContainer,
    handlePrestigeClick,
    handlePrestigeCancel,
    getPrestigeConfirmationClicks,
    clearPrestigeConfirmation,
    getPrestigeDisplayText
};
