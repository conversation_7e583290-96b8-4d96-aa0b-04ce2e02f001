const { GuildMember } = require("discord.js");
const { sendDehoistUsernameLog } = require("./sendLog.js");

/**
 * Get a random item from an array
 * @param {Array} array - Array to select from
 * @returns {*} Random item from the array
 */
function getRandomArrayItem(array) {
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * Basic dehoist function (used by rate-limited wrapper)
 * @param { GuildMember } member
 * @param { Object } data - Dehoist configuration
 * @returns { boolean } Success status
 */
module.exports = async function handleDehoist(member, data) {
    if(data.blocked.includes(member.displayName[0])){
        // Name
        if (data.enabled) { // Use standard enabled field
            let newUsername = member.displayName;
            if (data.blocked.includes(newUsername[0])) {
                const oldName = member.displayName;
                while (data.blocked.includes(newUsername[0]) && newUsername.length) newUsername = newUsername.slice(1);
                if (!newUsername.length) newUsername = getRandomArrayItem(data.names);
                try {
                    await member.setNickname(newUsername);

                    // Send dehoist username log
                    await sendDehoistUsernameLog(
                        member.guild.id,
                        member.user.id,
                        oldName,
                        newUsername,
                        member.client
                    );

                    console.log(`[dehoist] ✅ Successfully dehoisted ${oldName} → ${newUsername}`);
                    return true; // Success
                } catch (err) {
                    console.error('[dehoist] ❌ Failed to set nickname:', err);
                    return false; // Failed
                }
            }
        }
    }
    return false; // Not needed or not blocked
}
