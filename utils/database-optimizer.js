/**
 * Database Optimization Utility
 * This script helps optimize database operations across the bot
 */

const { mongoClient, getConnectionStats, performHealthCheck } = require('../mongo/client.js');

// Performance tracking
const operationMetrics = new Map();
const connectionHealthCache = { healthy: true, lastCheck: 0 };
const HEALTH_CHECK_INTERVAL = 30000; // 30 seconds

/**
 * Check connection health with caching
 */
async function checkConnectionHealth() {
    const now = Date.now();
    if (now - connectionHealthCache.lastCheck < HEALTH_CHECK_INTERVAL) {
        return connectionHealthCache.healthy;
    }

    try {
        const health = await performHealthCheck();
        connectionHealthCache.healthy = health.healthy;
        connectionHealthCache.lastCheck = now;
        return health.healthy;
    } catch (error) {
        connectionHealthCache.healthy = false;
        connectionHealthCache.lastCheck = now;
        return false;
    }
}

/**
 * Enhanced database operation wrapper with connection monitoring and performance tracking
 * @param {string} operation - Name of the operation for logging
 * @param {Function} dbOperation - Database operation function
 * @param {Object} options - Operation options
 * @returns {Promise<any>} Result of the database operation
 */
async function optimizedDbOperation(operation, dbOperation, options = {}) {
    const startTime = Date.now();
    const { retryOnFailure = true, maxRetries = 2 } = options;

    // Check connection health before expensive operations
    if (operation.includes('bulk') || operation.includes('aggregate')) {
        const isHealthy = await checkConnectionHealth();
        if (!isHealthy) {
            throw new Error('Database connection is unhealthy');
        }
    }

    let lastError;
    let attempt = 0;

    while (attempt <= maxRetries) {
        try {
            const result = await dbOperation();
            const duration = Date.now() - startTime;

            // Track operation metrics
            if (!operationMetrics.has(operation)) {
                operationMetrics.set(operation, {
                    count: 0,
                    totalDuration: 0,
                    avgDuration: 0,
                    maxDuration: 0,
                    errors: 0
                });
            }

            const metrics = operationMetrics.get(operation);
            metrics.count++;
            metrics.totalDuration += duration;
            metrics.avgDuration = metrics.totalDuration / metrics.count;
            metrics.maxDuration = Math.max(metrics.maxDuration, duration);

            // Log slow operations with more context
            if (duration > 100) {
                const connectionStats = getConnectionStats();
                console.warn(`[db-perf] Slow operation: ${operation} took ${duration}ms (attempt ${attempt + 1}/${maxRetries + 1}) - Active connections: ${connectionStats.activeConnections}`);
            }

            return result;
        } catch (error) {
            lastError = error;
            attempt++;

            // Track error metrics
            if (operationMetrics.has(operation)) {
                operationMetrics.get(operation).errors++;
            }

            // Don't retry on certain errors
            if (!retryOnFailure ||
                error.name === 'MongoServerError' && error.code === 11000 || // Duplicate key
                attempt > maxRetries) {
                break;
            }

            // Exponential backoff for retries
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
            console.warn(`[db-retry] Retrying ${operation} in ${delay}ms (attempt ${attempt}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    const duration = Date.now() - startTime;
    console.error(`[db-error] Operation ${operation} failed after ${duration}ms (${attempt} attempts):`, lastError);
    throw lastError;
}

/**
 * Batch database operations for better performance
 * @param {string} collectionName - Name of the collection
 * @param {Array} operations - Array of operations to batch
 * @returns {Promise<any>} Result of bulk operation
 */
async function batchDbOperations(collectionName, operations) {
    if (!operations || operations.length === 0) {
        return { acknowledged: true, insertedCount: 0, modifiedCount: 0 };
    }
    
    return optimizedDbOperation(`bulk-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.bulkWrite(operations, { ordered: false });
    });
}

/**
 * Optimized find operation with proper indexing hints
 * @param {string} collectionName - Collection name
 * @param {Object} query - Query object
 * @param {Object} options - Query options (sort, limit, etc.)
 * @returns {Promise<Array>} Query results
 */
async function optimizedFind(collectionName, query = {}, options = {}) {
    return optimizedDbOperation(`find-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        let cursor = col.find(query);

        // PERFORMANCE OPTIMIZATION: Add query hints for common patterns
        if (collectionName === 'user_inventory' && query.userId) {
            cursor = cursor.hint({ userId: 1, guildId: 1 }); // Use compound index
        } else if (collectionName === 'guild_config' && query.guildId) {
            cursor = cursor.hint({ guildId: 1 }); // Use guildId index
        } else if (collectionName === 'user_exp' && query.userId) {
            cursor = cursor.hint({ userId: 1, guildId: 1 }); // Use compound index
        }

        if (options.sort) cursor = cursor.sort(options.sort);
        if (options.limit) cursor = cursor.limit(options.limit);
        if (options.skip) cursor = cursor.skip(options.skip);

        return await cursor.toArray();
    });
}

/**
 * Optimized findOne operation
 * @param {string} collectionName - Collection name
 * @param {Object} query - Query object
 * @param {Object} options - Query options (projection, etc.)
 * @returns {Promise<Object|null>} Single document or null
 */
async function optimizedFindOne(collectionName, query, options = {}) {
    return optimizedDbOperation(`findOne-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.findOne(query, options);
    });
}

/**
 * Optimized update operation
 * @param {string} collectionName - Collection name
 * @param {Object} filter - Filter object
 * @param {Object} update - Update object
 * @param {Object} options - Update options
 * @returns {Promise<Object>} Update result
 */
async function optimizedUpdateOne(collectionName, filter, update, options = {}) {
    return optimizedDbOperation(`updateOne-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.updateOne(filter, update, options);
    });
}

/**
 * Optimized bulk update operation
 * @param {string} collectionName - Collection name
 * @param {Object} filter - Filter object
 * @param {Object} update - Update object
 * @param {Object} options - Update options
 * @returns {Promise<Object>} Update result
 */
async function optimizedUpdateMany(collectionName, filter, update, options = {}) {
    return optimizedDbOperation(`updateMany-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.updateMany(filter, update, options);
    });
}

/**
 * Optimized insert operation
 * @param {string} collectionName - Collection name
 * @param {Object} document - Document to insert
 * @returns {Promise<Object>} Insert result
 */
async function optimizedInsertOne(collectionName, document) {
    return optimizedDbOperation(`insertOne-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.insertOne(document);
    });
}

/**
 * Optimized delete operation
 * @param {string} collectionName - Collection name
 * @param {Object} filter - Filter object
 * @returns {Promise<Object>} Delete result
 */
async function optimizedDeleteOne(collectionName, filter) {
    return optimizedDbOperation(`deleteOne-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.deleteOne(filter);
    });
}

/**
 * Optimized findOneAndUpdate operation
 * @param {string} collectionName - Collection name
 * @param {Object} filter - Filter object
 * @param {Object} update - Update object
 * @param {Object} options - Update options
 * @returns {Promise<Object>} Update result
 */
async function optimizedFindOneAndUpdate(collectionName, filter, update, options = {}) {
    return optimizedDbOperation(`findOneAndUpdate-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.findOneAndUpdate(filter, update, options);
    });
}

/**
 * Optimized deleteMany operation
 * @param {string} collectionName - Collection name
 * @param {Object} filter - Filter object
 * @returns {Promise<Object>} Delete result
 */
async function optimizedDeleteMany(collectionName, filter) {
    return optimizedDbOperation(`deleteMany-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.deleteMany(filter);
    });
}

/**
 * Optimized countDocuments operation
 * @param {string} collectionName - Collection name
 * @param {Object} filter - Filter object
 * @param {Object} options - Count options
 * @returns {Promise<number>} Document count
 */
async function optimizedCountDocuments(collectionName, filter = {}, options = {}) {
    return optimizedDbOperation(`countDocuments-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.countDocuments(filter, options);
    });
}

/**
 * Get database performance statistics
 * @returns {Promise<Object>} Performance stats
 */
async function getDbPerformanceStats() {
    try {
        const db = mongoClient.db("seventeen_bot");
        const stats = await db.stats();
        
        return {
            collections: stats.collections,
            dataSize: Math.round(stats.dataSize / 1024 / 1024 * 100) / 100, // MB
            indexSize: Math.round(stats.indexSize / 1024 / 1024 * 100) / 100, // MB
            totalSize: Math.round((stats.dataSize + stats.indexSize) / 1024 / 1024 * 100) / 100, // MB
            objects: stats.objects
        };
    } catch (error) {
        console.error('[db-perf] Error getting performance stats:', error);
        return null;
    }
}

/**
 * Analyze slow queries and suggest optimizations
 * @returns {Promise<Array>} Array of optimization suggestions
 */
async function analyzeSlowQueries() {
    try {
        // This would require MongoDB profiling to be enabled
        // For now, return basic suggestions based on common patterns
        return [
            'Consider adding compound indexes for frequently queried fields',
            'Use projection to limit returned fields',
            'Implement pagination for large result sets',
            'Use aggregation pipelines for complex queries',
            'Consider caching frequently accessed data'
        ];
    } catch (error) {
        console.error('[db-perf] Error analyzing slow queries:', error);
        return [];
    }
}

/**
 * Optimized aggregate operation with performance monitoring and retry logic
 * @param {string} collectionName - Name of the collection
 * @param {Array} pipeline - Aggregation pipeline
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} Aggregation results
 */
async function optimizedAggregate(collectionName, pipeline, options = {}) {
    return optimizedDbOperation(`aggregate-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.aggregate(pipeline, options).toArray();
    });
}

/**
 * Optimized bulkWrite operation
 * @param {string} collectionName - Collection name
 * @param {Array} operations - Array of bulk operations
 * @param {Object} options - Bulk write options
 * @returns {Promise<Object>} Bulk write result
 */
async function optimizedBulkWrite(collectionName, operations, options = {}) {
    return optimizedDbOperation(`bulkWrite-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.bulkWrite(operations, options);
    });
}

/**
 * Optimized distinct operation
 * @param {string} collectionName - Collection name
 * @param {string} field - Field to get distinct values for
 * @param {Object} filter - Query filter
 * @returns {Promise<Array>} Distinct values
 */
async function optimizedDistinct(collectionName, field, filter = {}) {
    return optimizedDbOperation(`distinct-${collectionName}`, async () => {
        const col = mongoClient.db("seventeen_bot").collection(collectionName);
        return await col.distinct(field, filter);
    });
}

module.exports = {
    optimizedDbOperation,
    batchDbOperations,
    optimizedFind,
    optimizedFindOne,
    optimizedUpdateOne,
    optimizedUpdateMany,
    optimizedInsertOne,
    optimizedDeleteOne,
    optimizedDeleteMany,
    optimizedCountDocuments,
    optimizedFindOneAndUpdate,
    optimizedAggregate,
    optimizedBulkWrite,
    optimizedDistinct,
    getDbPerformanceStats,
    analyzeSlowQueries
};
