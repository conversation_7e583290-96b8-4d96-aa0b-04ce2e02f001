/**
 * Item Records System (Enterprise-Grade Performance Optimized)
 * Handles item leaderboards, parameter tracking, and discovery records with comprehensive optimization
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

// Only require MongoDB when actually needed
let mongoClient;

// Import caching utilities
const {
    getCachedItemLeaderboard,
    setCachedItemLeaderboard,
    invalidateLeaderboardCache
} = require('./itemCache.js');

// Import optimized database functions
const { optimizedInsertOne, optimizedCountDocuments, optimizedFindOne, optimizedAggregate, optimizedDistinct, optimizedBulkWrite } = require('./database-optimizer.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const itemRecordsMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    recordsProcessed: 0,
    leaderboardUpdates: 0,
    parallelOperations: 0,
    discoveryTracking: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const parameterRankCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for parameter rankings
const discoveryRankCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for discovery rankings
const leaderboardStatsCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for leaderboard statistics
const userRecordsCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user records

// Register caches for global cleanup
registerCache(parameterRankCache);
registerCache(discoveryRankCache);
registerCache(leaderboardStatsCache);
registerCache(userRecordsCache);

/**
 * Ensure database indexes exist for optimal performance
 * Call this once during bot startup
 */
async function ensureLeaderboardIndexes() {
    try {
        if (!mongoClient) {
            mongoClient = require('../mongo/client.js').mongoClient;
        }

        const col = mongoClient.db('seventeen_bot').collection('item_leaderboards');

        // Create compound indexes for efficient queries
        await col.createIndex({
            scope: 1,
            parameter: 1,
            itemType: 1,
            guildId: 1
        }, { background: true });

        await col.createIndex({
            scope: 1,
            parameter: 1,
            itemType: 1,
            userId: 1
        }, { background: true });

        await col.createIndex({
            scope: 1,
            parameter: 1,
            itemType: 1,
            numericValue: -1
        }, { background: true });

        // Create indexes for user_inventory collection (for live totals queries)
        const inventoryCol = mongoClient.db('seventeen_bot').collection('user_inventory');

        // Compound index for live totals queries (itemName + itemType + guildId)
        // Use same name as createIndexes.js to avoid conflicts
        await inventoryCol.createIndex({
            itemName: 1,
            itemType: 1,
            guildId: 1
        }, {
            name: 'itemName_itemType_guildId',
            background: true
        }).catch(err => {
            // Ignore if index already exists
            if (err.code !== 85) {
                console.error('[itemLeaderboards] Error creating itemName_itemType_guildId index:', err);
            }
        });

        // Index for parameter existence queries
        await inventoryCol.createIndex({
            itemName: 1,
            itemType: 1,
            guildId: 1,
            'catchData.weight': 1
        }, { background: true, sparse: true });

        await inventoryCol.createIndex({
            itemName: 1,
            itemType: 1,
            guildId: 1,
            'catchData.length': 1
        }, { background: true, sparse: true });

        await inventoryCol.createIndex({
            itemName: 1,
            itemType: 1,
            guildId: 1,
            'catchData.purity': 1
        }, { background: true, sparse: true });

        await inventoryCol.createIndex({
            itemName: 1,
            itemType: 1,
            guildId: 1,
            'catchData.condition': 1
        }, { background: true, sparse: true });

        console.log('[itemLeaderboards] Database indexes ensured');

    } catch (error) {
        console.error('[itemLeaderboards] Error creating indexes:', error);
    }
}

/**
 * Item Parameter Leaderboard System - Backend Only
 * Tracks records for item parameters like weight, length, etc.
 * Supports both guild and global leaderboards
 */

/**
 * Parse a parameter value to extract numeric value for comparison
 * @param {string} paramValue - Parameter value (e.g., "47.5lbs", "18.2inches")
 * @returns {number|null} Numeric value or null if not parseable
 */
function parseParameterValue(paramValue) {
    if (!paramValue || typeof paramValue !== 'string') return null;
    
    // Extract number from string (handles decimals)
    const match = paramValue.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : null;
}

/**
 * Update leaderboards when a new item is caught (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing, enhanced caching, and comprehensive performance monitoring
 * @param {Object} inventoryItem - The inventory item that was just added
 * @returns {Object} Leaderboard results { guildRecords: [], globalRecords: [], guildRanks: {}, globalRanks: {} }
 */
async function updateItemLeaderboards(inventoryItem) {
    const startTime = Date.now();
    itemRecordsMetrics.recordsProcessed++;
    itemRecordsMetrics.leaderboardUpdates++;

    try {
        // Lazy load MongoDB client
        if (!mongoClient) {
            mongoClient = require('../mongo/client.js').mongoClient;
        }

        const { userId, guildId, itemName, itemType, catchData } = inventoryItem;

        if (!catchData || Object.keys(catchData).length === 0) {
            // For items without parameters, still track discovery order
            return await trackDiscoveryOnly(userId, guildId, itemName, itemType);
        }

        const results = {
            guildRecords: [],
            globalRecords: [],
            guildRanks: {},
            globalRanks: {}
        };

        // OPTIMIZED: Enhanced parallel processing with comprehensive error handling
        itemRecordsMetrics.parallelOperations++;

        // FIRST: Track overall item discovery (independent of parameters) in parallel
        const discoveryPromises = [
            trackItemDiscovery('guild', guildId, userId, itemName, itemType),
            trackItemDiscovery('global', null, userId, itemName, itemType)
        ];

        // SECOND: Process parameters for performance rankings in parallel
        const parameterPromises = Object.entries(catchData).map(async ([paramName, paramValue]) => {
            const numericValue = parseParameterValue(paramValue);
            if (numericValue === null) return null;

            // Process guild and global leaderboards in parallel
            const [guildResult, globalResult] = await Promise.allSettled([
                updateParameterLeaderboard(
                    'guild', guildId, userId, itemName, itemType, paramName, numericValue, paramValue
                ),
                updateParameterLeaderboard(
                    'global', null, userId, itemName, itemType, paramName, numericValue, paramValue
                )
            ]);

            return {
                paramName,
                paramValue,
                guildResult: guildResult.status === 'fulfilled' ? guildResult.value : { isRecord: false, rank: 0, total: 0 },
                globalResult: globalResult.status === 'fulfilled' ? globalResult.value : { isRecord: false, rank: 0, total: 0 }
            };
        });

        // OPTIMIZED: Execute discovery and parameter processing in parallel
        const [discoveryResults, parameterResults] = await Promise.allSettled([
            Promise.allSettled(discoveryPromises),
            Promise.allSettled(parameterPromises)
        ]);

        // Process discovery results
        if (discoveryResults.status === 'fulfilled') {
            const [itemGuildDiscovery, itemGlobalDiscovery] = discoveryResults.value;

            results.guildRanks['_item_discovery'] = itemGuildDiscovery.status === 'fulfilled' ?
                itemGuildDiscovery.value : { rank: 0, total: 0, discoveryRank: 0 };
            results.globalRanks['_item_discovery'] = itemGlobalDiscovery.status === 'fulfilled' ?
                itemGlobalDiscovery.value : { rank: 0, total: 0, discoveryRank: 0 };
        }

        // Process parameter results
        if (parameterResults.status === 'fulfilled') {
            parameterResults.value.forEach(result => {
                if (result.status !== 'fulfilled' || !result.value) return;

                const { paramName, paramValue, guildResult, globalResult } = result.value;

                if (guildResult.isRecord) {
                    results.guildRecords.push({
                        parameter: paramName,
                        value: paramValue,
                        rank: guildResult.rank,
                        total: guildResult.total
                    });
                }
                results.guildRanks[paramName] = { rank: guildResult.rank, total: guildResult.total };

                if (globalResult.isRecord) {
                    results.globalRecords.push({
                        parameter: paramName,
                        value: paramValue,
                        rank: globalResult.rank,
                        total: globalResult.total
                    });
                }
                results.globalRanks[paramName] = { rank: globalResult.rank, total: globalResult.total };
            });
        }

        // CRITICAL FIX: Invalidate leaderboard cache after record updates to ensure fresh rankings
        try {
            const { invalidateLeaderboardCache } = require('./itemCache.js');
            invalidateLeaderboardCache(itemName);
            console.log(`[itemRecords] 🔄 Invalidated leaderboard cache for ${itemName}`);
        } catch (cacheError) {
            console.error('[itemRecords] Error invalidating leaderboard cache:', cacheError);
        }

        const duration = Date.now() - startTime;
        if (itemRecordsMetrics.verboseLogging || duration > 200) {
            console.log(`[itemRecords] ✅ Leaderboards updated for ${itemName}: ${results.guildRecords.length + results.globalRecords.length} records (${duration}ms)`);
        }

        return results;

    } catch (error) {
        console.error('[itemRecords] ❌ Error updating leaderboards:', error);
        return { guildRecords: [], globalRecords: [], guildRanks: {}, globalRanks: {} };
    }
}

/**
 * Track item discovery order with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: LRU caching with performance monitoring and batch operations
 * @param {string} scope - 'guild' or 'global'
 * @param {string|null} guildId - Guild ID (null for global)
 * @param {string} userId - User ID
 * @param {string} itemName - Item name
 * @param {string} itemType - Item type
 * @returns {Object} Discovery ranking data
 */
async function trackItemDiscovery(scope, guildId, userId, itemName, itemType) {
    const startTime = Date.now();
    itemRecordsMetrics.discoveryTracking++;

    try {
        const cacheKey = `discovery_${scope}_${guildId || 'global'}_${itemName}_${itemType}`;

        // Check cache for recent discovery data
        const cached = discoveryRankCache.get(cacheKey);
        if (cached) {
            itemRecordsMetrics.cacheHits++;
            if (itemRecordsMetrics.verboseLogging) {
                console.log(`[itemRecords] ⚡ Discovery cache hit for ${cacheKey} (${Date.now() - startTime}ms)`);
            }
            // For cached data, we still need to insert the new record but can estimate rank
        }

        itemRecordsMetrics.cacheMisses++;
        itemRecordsMetrics.databaseQueries++;

        // Create item discovery entry
        const entry = {
            scope: scope,
            guildId: guildId,
            userId: userId,
            itemName: itemName,
            itemType: itemType,
            parameter: '_item_discovery', // Special parameter for item discovery
            numericValue: 0, // Not used for discovery
            displayValue: 'found',
            recordedAt: new Date()
        };

        // FIXED: Sequential execution to prevent race conditions causing rank > total
        console.log(`[itemRecords] 🔍 DISCOVERY TRACKING START: ${scope} ${itemName} for ${userId} in ${guildId || 'global'}`);
        const preInsertTime = Date.now();

        // Insert the discovery record first
        const insertResult = await optimizedInsertOne('item_records', entry);
        const insertedId = insertResult.insertedId;

        console.log(`[itemRecords] ✅ Discovery record inserted: ${insertedId} (${Date.now() - preInsertTime}ms)`);

        // FIXED: Calculate both rank and total AFTER insert to ensure consistency
        const postInsertTime = Date.now();
        const [discoveryRankResult, totalCountResult] = await Promise.allSettled([
            // Calculate discovery rank (chronological order) - how many came before this one
            optimizedCountDocuments('item_records', {
                scope: scope,
                itemName: itemName,
                itemType: itemType,
                parameter: '_item_discovery',
                _id: { $lt: insertedId },
                ...(scope === 'guild' ? { guildId: guildId } : {})
            }),
            // Calculate total count AFTER insert to include this record
            optimizedCountDocuments('item_records', {
                scope: scope,
                itemName: itemName,
                itemType: itemType,
                parameter: '_item_discovery',
                ...(scope === 'guild' ? { guildId: guildId } : {})
            })
        ]);

        const discoveryRank = (discoveryRankResult.status === 'fulfilled' ? discoveryRankResult.value : 0) + 1;
        const totalCount = totalCountResult.status === 'fulfilled' ? totalCountResult.value : 1;

        console.log(`[itemRecords] 📊 Discovery calculations: rank=${discoveryRank}, total=${totalCount} (${Date.now() - postInsertTime}ms)`);

        // CRITICAL: Validate data consistency before returning
        if (discoveryRank > totalCount) {
            console.error(`[itemRecords] 🚨 DATA INCONSISTENCY DETECTED: ${scope} ${itemName} - Rank ${discoveryRank} > Total ${totalCount}`);
            console.error(`[itemRecords] 🔍 Debug info: insertedId=${insertedId}, userId=${userId}, guildId=${guildId}`);

            // Force recalculation with fresh database query
            const freshTotal = await optimizedCountDocuments('item_records', {
                scope: scope,
                itemName: itemName,
                itemType: itemType,
                parameter: '_item_discovery',
                ...(scope === 'guild' ? { guildId: guildId } : {})
            });

            console.log(`[itemRecords] 🔄 Fresh total count: ${freshTotal}`);

            // Use the fresh total if it's more accurate
            const correctedTotal = Math.max(discoveryRank, freshTotal);
            console.log(`[itemRecords] ✅ Using corrected total: ${correctedTotal}`);

            return {
                rank: discoveryRank,
                total: correctedTotal,
                discoveryRank: discoveryRank
            };
        }

        const result = {
            rank: discoveryRank, // For discovery, rank = discoveryRank
            total: totalCount,
            discoveryRank: discoveryRank
        };

        // Cache the discovery statistics for future reference
        discoveryRankCache.set(cacheKey, {
            total: totalCount,
            lastUpdate: Date.now()
        });

        const duration = Date.now() - startTime;
        if (itemRecordsMetrics.verboseLogging || duration > 100) {
            console.log(`[itemRecords] ✅ Discovery tracked for ${itemName}: rank ${discoveryRank}/${totalCount} (${duration}ms)`);
        }

        return result;

    } catch (error) {
        console.error('[itemRecords] ❌ Error tracking item discovery:', error);
        return { rank: 0, total: 0, discoveryRank: 0 };
    }
}

/**
 * Track discovery order for items without parameters (uses item discovery)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {string} itemName - Item name
 * @param {string} itemType - Item type
 * @returns {Object} Leaderboard results with discovery rankings
 */
async function trackDiscoveryOnly(userId, guildId, itemName, itemType) {
    try {
        // For items without parameters, just track item discovery
        const [guildDiscovery, globalDiscovery] = await Promise.all([
            trackItemDiscovery('guild', guildId, userId, itemName, itemType),
            trackItemDiscovery('global', null, userId, itemName, itemType)
        ]);

        return {
            guildRecords: [],
            globalRecords: [],
            guildRanks: {
                '_item_discovery': guildDiscovery
            },
            globalRanks: {
                '_item_discovery': globalDiscovery
            }
        };

    } catch (error) {
        console.error('[itemLeaderboards] Error tracking discovery only:', error);
        return { guildRecords: [], globalRecords: [], guildRanks: {}, globalRanks: {} };
    }
}

/**
 * Update parameter leaderboard with batch optimization (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel database operations, enhanced caching, and comprehensive performance monitoring
 * @param {string} scope - 'guild' or 'global'
 * @param {string|null} guildId - Guild ID (null for global)
 * @param {string} userId - User ID
 * @param {string} itemName - Item name
 * @param {string} itemType - Item type
 * @param {string} paramName - Parameter name (weight, length, etc.)
 * @param {number} numericValue - Numeric value for comparison
 * @param {string} displayValue - Original display value
 * @returns {Object} { isRecord: boolean, rank: number, total: number, discoveryRank: number }
 */
async function updateParameterLeaderboard(scope, guildId, userId, itemName, itemType, paramName, numericValue, displayValue) {
    const startTime = Date.now();

    try {
        const cacheKey = `param_${scope}_${guildId || 'global'}_${itemName}_${itemType}_${paramName}`;

        // Check cache for recent parameter statistics
        const cached = parameterRankCache.get(cacheKey);
        if (cached) {
            itemRecordsMetrics.cacheHits++;
            if (itemRecordsMetrics.verboseLogging) {
                console.log(`[itemRecords] ⚡ Parameter cache hit for ${cacheKey} (${Date.now() - startTime}ms)`);
            }
        } else {
            itemRecordsMetrics.cacheMisses++;
        }

        itemRecordsMetrics.databaseQueries++;

        // Build query for this parameter type
        const query = {
            scope: scope,
            parameter: paramName,
            itemType: itemType
        };

        if (scope === 'guild') {
            query.guildId = guildId;
        }

        // Create the entry
        const entry = {
            scope: scope,
            guildId: guildId,
            userId: userId,
            itemName: itemName,
            itemType: itemType,
            parameter: paramName,
            numericValue: numericValue,
            displayValue: displayValue,
            recordedAt: new Date()
        };

        // OPTIMIZED: Execute all database operations in parallel
        const [insertResult, totalCountResult, betterCountResult, discoveryRankResult] = await Promise.allSettled([
            optimizedInsertOne('item_records', entry),

            // 1. PARAMETER RANKING: Count total items
            optimizedCountDocuments('item_records', {
                scope: scope,
                itemName: itemName,
                itemType: itemType,
                parameter: paramName,
                ...(scope === 'guild' ? { guildId: guildId } : {})
            }),

            // 2. Count how many items have better values than this one
            optimizedCountDocuments('item_records', {
                scope: scope,
                itemName: itemName,
                itemType: itemType,
                parameter: paramName,
                numericValue: { $gt: numericValue },
                ...(scope === 'guild' ? { guildId: guildId } : {})
            }),

            // 3. DISCOVERY RANKING: Count chronological order
            optimizedCountDocuments('item_records', {
                scope: scope,
                itemName: itemName,
                itemType: itemType,
                parameter: paramName,
                recordedAt: { $lt: entry.recordedAt },
                ...(scope === 'guild' ? { guildId: guildId } : {})
            })
        ]);

        // Extract results with fallback values
        const totalCount = totalCountResult.status === 'fulfilled' ? totalCountResult.value : 1;
        const betterCount = betterCountResult.status === 'fulfilled' ? betterCountResult.value : 0;
        const discoveryRank = discoveryRankResult.status === 'fulfilled' ? discoveryRankResult.value + 1 : 1;

        // 4. RECORD CHECK: Is this the best value ever?
        const isRecord = betterCount === 0 && totalCount > 1; // Record only if there are other items to beat

        const result = {
            isRecord: isRecord,
            rank: betterCount + 1, // Performance ranking (1st = best value)
            total: totalCount,
            discoveryRank: discoveryRank // Chronological ranking (1st = first caught)
        };

        // Cache the parameter statistics for future reference
        parameterRankCache.set(cacheKey, {
            totalCount: totalCount,
            lastUpdate: Date.now(),
            bestValue: numericValue
        });

        const duration = Date.now() - startTime;
        if (itemRecordsMetrics.verboseLogging || duration > 150) {
            console.log(`[itemRecords] ✅ Parameter leaderboard updated for ${paramName}: rank ${result.rank}/${totalCount} ${isRecord ? '(NEW RECORD!)' : ''} (${duration}ms)`);
        }

        return result;

    } catch (error) {
        console.error('[itemRecords] ❌ Error updating parameter leaderboard:', error);
        return { isRecord: false, rank: 0, total: 0 };
    }
}



/**
 * Get user's rank for a specific parameter with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: LRU caching with performance monitoring and parallel operations
 * @param {string} scope - 'guild' or 'global'
 * @param {string|null} guildId - Guild ID (null for global)
 * @param {string} userId - User ID
 * @param {string} paramName - Parameter name
 * @param {string} itemType - Item type
 * @returns {Object} { rank: number, total: number, value: string }
 */
async function getUserParameterRank(scope, guildId, userId, paramName, itemType) {
    const startTime = Date.now();
    const cacheKey = `user_rank_${scope}_${guildId || 'global'}_${userId}_${paramName}_${itemType}`;

    try {
        // Check cache first
        const cached = userRecordsCache.get(cacheKey);
        if (cached) {
            itemRecordsMetrics.cacheHits++;
            if (itemRecordsMetrics.verboseLogging) {
                console.log(`[itemRecords] ⚡ User rank cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        itemRecordsMetrics.cacheMisses++;
        itemRecordsMetrics.databaseQueries++;

        // Build query
        const query = {
            scope: scope,
            parameter: paramName,
            itemType: itemType
        };

        if (scope === 'guild') {
            query.guildId = guildId;
        }

        // OPTIMIZED: Execute user lookup and total users count in parallel
        const [userBestResult, totalUsersResult] = await Promise.allSettled([
            optimizedFindOne('item_records',
                { ...query, userId: userId },
                { sort: { numericValue: -1 } }
            ),
            optimizedDistinct('item_records', 'userId', query)
        ]);

        const userBest = userBestResult.status === 'fulfilled' ? userBestResult.value : null;
        const totalUsers = totalUsersResult.status === 'fulfilled' ? totalUsersResult.value : [];

        if (!userBest) {
            const result = { rank: 0, total: totalUsers.length, value: null };
            userRecordsCache.set(cacheKey, result);
            return result;
        }

        // Count how many unique users have better values
        const betterUsers = await optimizedAggregate('item_records', [
            { $match: { ...query, numericValue: { $gt: userBest.numericValue } } },
            { $group: { _id: '$userId' } },
            { $count: 'count' }
        ]);

        const betterCount = betterUsers.length > 0 ? betterUsers[0].count : 0;

        const result = {
            rank: betterCount + 1,
            total: totalUsers.length,
            value: userBest.displayValue
        };

        // Cache the result
        userRecordsCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        if (itemRecordsMetrics.verboseLogging || duration > 100) {
            console.log(`[itemRecords] ✅ User rank calculated for ${userId}: #${result.rank}/${result.total} (${duration}ms)`);
        }

        return result;

    } catch (error) {
        console.error('[itemRecords] ❌ Error getting user rank:', error);
        return { rank: 0, total: 0, value: null };
    }
}

// Test the parameter parsing
if (require.main === module) {
    // Test cases
    const testValues = [
        "47.5lbs",
        "18.2inches",
        "95%",
        "2-5 lbs", // This won't parse as a single value, which is correct
        "pristine",
        "123.45kg",
        "not a number"
    ];

    console.log('Testing parameter parsing:');
    testValues.forEach(val => {
        const parsed = parseParameterValue(val);
        console.log(`"${val}" -> ${parsed}`);
    });
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = itemRecordsMetrics.cacheHits + itemRecordsMetrics.cacheMisses > 0 ?
        (itemRecordsMetrics.cacheHits / (itemRecordsMetrics.cacheHits + itemRecordsMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: itemRecordsMetrics.cacheHits,
            cacheMisses: itemRecordsMetrics.cacheMisses,
            databaseQueries: itemRecordsMetrics.databaseQueries,
            averageQueryTime: `${itemRecordsMetrics.averageQueryTime.toFixed(2)}ms`,
            recordsProcessed: itemRecordsMetrics.recordsProcessed,
            leaderboardUpdates: itemRecordsMetrics.leaderboardUpdates,
            parallelOperations: itemRecordsMetrics.parallelOperations,
            discoveryTracking: itemRecordsMetrics.discoveryTracking,
            lastOptimization: new Date(itemRecordsMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            parameterRank: parameterRankCache.getStats(),
            discoveryRank: discoveryRankCache.getStats(),
            leaderboardStats: leaderboardStatsCache.getStats(),
            userRecords: userRecordsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            parameterRank: parameterRankCache.getStats().memoryUsage,
            discoveryRank: discoveryRankCache.getStats().memoryUsage,
            leaderboardStats: leaderboardStatsCache.getStats().memoryUsage,
            userRecords: userRecordsCache.getStats().memoryUsage,
            total: parameterRankCache.getStats().memoryUsage +
                   discoveryRankCache.getStats().memoryUsage +
                   leaderboardStatsCache.getStats().memoryUsage +
                   userRecordsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, itemRecordsMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 100) {
        recommendations.push('Database queries are slow - consider adding indexes or optimizing queries');
    }

    if (metrics.databaseQueries > metrics.cacheHits * 2) {
        recommendations.push('High database query ratio - consider caching more aggressively');
    }

    if (metrics.parallelOperations < metrics.leaderboardUpdates * 0.8) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    itemRecordsMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[itemRecords] 📊 Performance Report:`);
    console.log(`[itemRecords]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[itemRecords]   Records Processed: ${stats.performance.recordsProcessed}`);
    console.log(`[itemRecords]   Leaderboard Updates: ${stats.performance.leaderboardUpdates}`);
    console.log(`[itemRecords]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[itemRecords]   Discovery Tracking: ${stats.performance.discoveryTracking}`);
    console.log(`[itemRecords]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[itemRecords]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[itemRecords]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[itemRecords] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all item records caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllItemRecordsCaches() {
    parameterRankCache.clear();
    discoveryRankCache.clear();
    leaderboardStatsCache.clear();
    userRecordsCache.clear();

    console.log('[itemRecords] 🗑️ Cleared all item records caches');
}

/**
 * Invalidate specific item caches
 * @param {string} itemName - Item name
 * @param {string} itemType - Item type
 */
function invalidateItemCaches(itemName, itemType) {
    // FIXED: Use LRU cache methods instead of Map methods
    const keys = [
        ...parameterRankCache.getKeysByAccessTime().filter(key => key.includes(`_${itemName}_${itemType}_`)),
        ...discoveryRankCache.getKeysByAccessTime().filter(key => key.includes(`_${itemName}_${itemType}`)),
        ...userRecordsCache.getKeysByAccessTime().filter(key => key.includes(`_${itemType}`))
    ];

    keys.forEach(key => {
        if (key.includes('param_')) parameterRankCache.delete(key);
        if (key.includes('discovery_')) discoveryRankCache.delete(key);
        if (key.includes('user_rank_')) userRecordsCache.delete(key);
    });

    if (itemRecordsMetrics.verboseLogging) {
        console.log(`[itemRecords] 🗑️ Invalidated ${keys.length} cache entries for ${itemName} (${itemType})`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, itemRecordsMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    updateItemLeaderboards,
    getUserParameterRank,
    parseParameterValue,
    ensureLeaderboardIndexes,
    trackItemDiscovery,
    trackDiscoveryOnly,
    updateParameterLeaderboard,

    // Enhanced optimization functions
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllItemRecordsCaches,
    invalidateItemCaches,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...itemRecordsMetrics })
};
