const { mongoClient } = require('../mongo/client.js');
const { optimizedUpdateOne, optimizedCountDocuments, optimizedFind, optimizedAggregate } = require('./database-optimizer.js');
const { getCachedGlobalUser } = require('./globalLevels.js');
const { OPERATION_COLORS, LOG_COLORS } = require('./colors.js');
const { ButtonBuilder, ButtonStyle, ActionRowBuilder, ContainerBuilder, TextDisplayBuilder, SeparatorBuilder } = require('discord.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const starfallMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    claimsProcessed: 0,
    itemDropsProcessed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const starfallDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for starfall data
const rewardCalculationCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for reward calculations
const itemDropCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for item drop data
const claimValidationCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for claim validation

// Register caches for global cleanup
registerCache(starfallDataCache);
registerCache(rewardCalculationCache);
registerCache(itemDropCache);
registerCache(claimValidationCache);

// RACE CONDITION FIX: Track active claim operations to prevent multiple simultaneous claims
const activeClaimOperations = new Set();

// Bot owner testing mode toggle
let ownerTestingMode = false;

/**
 * Starfall Daily Reward System (Enterprise-Grade Performance Optimized)
 * Provides daily star rewards with streak progression and item drops
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

// Streak progression table
const STREAK_PROGRESSION = {
    1: { rows: 5, buttons: 25, baseStars: 15 },  // Day 1: 25 buttons (5x5 grid) - 3 text + 5 rows + 25 buttons + 2 hub = 35 total
    2: { rows: 5, buttons: 25, baseStars: 18 },  // Day 2: 25 buttons (5x5 grid) - consistent experience
    3: { rows: 5, buttons: 24, baseStars: 21 },  // Day 3: 24 buttons (5 rows, last row has 4) - slight decrease
    4: { rows: 4, buttons: 20, baseStars: 24 },  // Day 4: 20 buttons (4x5 grid) - 3 text + 4 rows + 20 buttons + 2 hub = 29 total
    5: { rows: 4, buttons: 20, baseStars: 27 },  // Day 5: 20 buttons (4x5 grid) - maintain experience longer
    6: { rows: 3, buttons: 15, baseStars: 30 },  // Day 6: 15 buttons (3x5 grid) - 3 text + 3 rows + 15 buttons + 2 hub = 23 total
    7: { rows: 2, buttons: 10, baseStars: 33 }   // Day 7+: 10 buttons (2x5 grid) - minimum for long streaks
};

// Multiplier odds by streak day
const MULTIPLIER_ODDS = {
    1: [{ multiplier: 1.0, weight: 90 }, { multiplier: 1.5, weight: 10 }],
    2: [{ multiplier: 1.0, weight: 85 }, { multiplier: 1.5, weight: 15 }],
    3: [{ multiplier: 1.0, weight: 80 }, { multiplier: 1.5, weight: 20 }],
    4: [{ multiplier: 1.5, weight: 85 }, { multiplier: 2.0, weight: 15 }],
    5: [{ multiplier: 1.5, weight: 80 }, { multiplier: 2.0, weight: 20 }],
    6: [{ multiplier: 1.5, weight: 75 }, { multiplier: 2.0, weight: 25 }],
    7: [{ multiplier: 2.0, weight: 80 }, { multiplier: 2.5, weight: 15 }, { multiplier: 3.0, weight: 5 }]  // 7+ uses this
};

// Item drop chances by streak day
const ITEM_DROP_CHANCES = {
    1: 1.5, 2: 2.0, 3: 2.5, 4: 3.0, 5: 3.5, 6: 4.0, 7: 5.0  // 7+ uses 5.0%
};

/**
 * Get streak progression data for a given day
 * @param {number} streakDay - Current streak day
 * @returns {Object} Progression data with rows, buttons, and baseStars
 */
function getStreakProgression(streakDay) {
    const day = Math.min(Math.max(streakDay, 1), 7);
    return STREAK_PROGRESSION[day] || STREAK_PROGRESSION[7];
}

/**
 * Get base stars for a streak day
 * @param {number} streakDay - Current streak day
 * @returns {number} Base star amount
 */
function getBaseStarsForStreak(streakDay) {
    return getStreakProgression(streakDay).baseStars;
}

/**
 * Get number of button rows for a streak day
 * @param {number} streakDay - Current streak day
 * @returns {number} Number of button rows
 */
function getRowsForStreak(streakDay) {
    return getStreakProgression(streakDay).rows;
}

/**
 * Get total number of buttons for a streak day
 * @param {number} streakDay - Current streak day
 * @returns {number} Total button count
 */
function getButtonsForStreak(streakDay) {
    return getStreakProgression(streakDay).buttons;
}

/**
 * Roll for multiplier based on streak day odds
 * @param {number} streakDay - Current streak day
 * @returns {number} Multiplier value
 */
function rollMultiplier(streakDay) {
    const day = Math.min(Math.max(streakDay, 1), 7);
    const odds = MULTIPLIER_ODDS[day] || MULTIPLIER_ODDS[7];
    
    // Calculate total weight
    const totalWeight = odds.reduce((sum, option) => sum + option.weight, 0);
    
    // Roll random number
    const roll = Math.random() * totalWeight;
    
    // Find which multiplier was rolled
    let currentWeight = 0;
    for (const option of odds) {
        currentWeight += option.weight;
        if (roll <= currentWeight) {
            return option.multiplier;
        }
    }
    
    // Fallback to first option
    return odds[0].multiplier;
}

/**
 * Get item drop chance for a streak day
 * @param {number} streakDay - Current streak day
 * @returns {number} Drop chance percentage
 */
function getItemDropChance(streakDay) {
    const day = Math.min(Math.max(streakDay, 1), 7);
    return ITEM_DROP_CHANCES[day] || ITEM_DROP_CHANCES[7];
}

/**
 * Roll for item drop based on streak day
 * @param {number} streakDay - Current streak day
 * @returns {boolean} Whether an item should drop
 */
function rollItemDrop(streakDay) {
    const dropChance = getItemDropChance(streakDay);
    const roll = Math.random() * 100;
    return roll <= dropChance;
}

/**
 * Calculate complete Starfall reward for a streak day
 * @param {number} streakDay - Current streak day
 * @returns {Object} Reward data with stars, multiplier, and itemDropped flag
 */
function calculateStarfallReward(streakDay) {
    const baseStars = getBaseStarsForStreak(streakDay);
    const multiplier = rollMultiplier(streakDay);
    const finalStars = Math.floor(baseStars * multiplier);
    const itemDropped = rollItemDrop(streakDay);
    
    return {
        stars: finalStars,
        baseStars,
        multiplier,
        itemDropped
    };
}

/**
 * Toggle owner testing mode on/off
 * @param {boolean} enabled - Whether to enable testing mode
 * @returns {boolean} New testing mode state
 */
function setOwnerTestingMode(enabled) {
    ownerTestingMode = enabled;
    console.log(`[starfall] 🧪 Owner testing mode ${enabled ? 'ENABLED' : 'DISABLED'}`);
    return ownerTestingMode;
}

/**
 * Get current owner testing mode state
 * @returns {boolean} Whether testing mode is enabled
 */
function getOwnerTestingMode() {
    return ownerTestingMode;
}

/**
 * Check if user can claim daily reward
 * @param {Object} starfallData - User's starfall data
 * @param {string} userId - User ID for owner testing bypass (bot owner can bypass cooldowns when testing mode is enabled)
 * @returns {boolean} Whether user can claim
 */
function canClaimDaily(starfallData, userId = null) {
    // Check if already claimed today using todayClaimData (with owner bypass)
    const todayClaimData = getTodayClaimData(starfallData, userId);
    if (todayClaimData && todayClaimData.claimed) {
        return false; // Already claimed today
    }

    // If no claim data for today, user can claim
    // (Owner testing mode bypass is handled in getTodayClaimData)
    if (userId && userId === process.env.OWNER && ownerTestingMode) {
        console.log(`[starfall] 🧪 Bot owner testing mode bypass activated for ${userId}`);
    }
    return true;
}

/**
 * Check if streak should be reset (more than 48 hours since last claim)
 * @param {Object} starfallData - User's starfall data
 * @returns {boolean} Whether streak should reset
 */
function shouldResetStreak(starfallData) {
    if (!starfallData.lastClaimDate) {
        return false; // First time claiming, no reset needed
    }
    
    const now = new Date();
    const lastClaim = new Date(starfallData.lastClaimDate);
    
    // Reset streak if more than 48 hours have passed
    const hoursSinceLastClaim = (now - lastClaim) / (1000 * 60 * 60);
    return hoursSinceLastClaim > 48;
}

/**
 * Get today's claim data if it exists
 * @param {Object} starfallData - User's starfall data
 * @param {string} userId - User ID for owner testing bypass
 * @returns {Object|null} Today's claim data or null
 */
function getTodayClaimData(starfallData, userId = null) {
    // Owner testing mode bypass - act like no claim data exists (fresh day)
    if (userId && userId === process.env.OWNER && ownerTestingMode) {
        return null;
    }

    if (!starfallData.todayClaimData) return null;

    // CRITICAL FIX: Use UTC dates for consistency with getNextClaimTimestamp
    const now = new Date();
    const today = `${now.getUTCFullYear()}-${String(now.getUTCMonth() + 1).padStart(2, '0')}-${String(now.getUTCDate()).padStart(2, '0')}`;

    const claimDate = new Date(starfallData.todayClaimData.date);
    const claimDateUTC = `${claimDate.getUTCFullYear()}-${String(claimDate.getUTCMonth() + 1).padStart(2, '0')}-${String(claimDate.getUTCDate()).padStart(2, '0')}`;

    // Return claim data only if it's from today (UTC)
    return claimDateUTC === today ? starfallData.todayClaimData : null;
}

/**
 * Convert timestamp to human-readable relative time for select menu descriptions
 * Discord timestamp formatting doesn't work in select menus, so we need plain text
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Human-readable relative time like "in 4 hours" or "in 23 minutes"
 */
function formatRelativeTime(timestamp) {
    const now = Math.floor(Date.now() / 1000);
    const diff = timestamp - now;

    if (diff <= 0) {
        return 'now';
    }

    const minutes = Math.floor(diff / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        return `in ${days} day${days === 1 ? '' : 's'}`;
    } else if (hours > 0) {
        return `in ${hours} hour${hours === 1 ? '' : 's'}`;
    } else if (minutes > 0) {
        return `in ${minutes} minute${minutes === 1 ? '' : 's'}`;
    } else {
        return 'in less than a minute';
    }
}

/**
 * Get dynamic menu description for starfall feature
 * @param {string} userId - User ID
 * @returns {Promise<string>} Menu description text
 */
async function getStarfallMenuDescription(userId) {
    try {
        const starfallData = await getStarfallData(userId);

        if (canClaimDaily(starfallData, userId)) {
            return 'starfall available';
        } else {
            const nextClaimTimestamp = getNextClaimTimestamp(starfallData);
            const relativeTime = formatRelativeTime(nextClaimTimestamp);
            return `next starfall ${relativeTime}`;
        }
    } catch (error) {
        console.error('Error getting starfall menu description:', error);
        return 'daily star rewards and streaks';
    }
}

/**
 * Get next claim timestamp (next midnight UTC)
 * @param {Object} starfallData - User's starfall data
 * @returns {number} Unix timestamp for next claim
 */
function getNextClaimTimestamp(starfallData) {
    // Always return next midnight UTC, regardless of claim availability
    // This maintains consistent timestamps for UI display
    const now = new Date();
    const nextMidnight = new Date(now);
    nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
    nextMidnight.setUTCHours(0, 0, 0, 0);

    return Math.floor(nextMidnight.getTime() / 1000);
}

/**
 * Get default starfall data for new users
 * @returns {Object} Default starfall data structure
 */
function getDefaultStarfallData() {
    return {
        stars: 0,
        currentStreak: 0,
        longestStreak: 0,
        lastClaimDate: null,
        totalClaims: 0,
        itemsFound: 0,
        todayClaimData: null, // Stores today's claim result for button reveal
        pendingStreakIncrement: null // CRITICAL FIX: Stores pending streak increment for next day application
    };
}

/**
 * Initialize or get user's starfall data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User's starfall data
 */
async function getStarfallData(userId) {
    const startTime = Date.now();
    const cacheKey = `starfall_${userId}`;

    try {
        // Check cache first
        const cached = starfallDataCache.get(cacheKey);
        if (cached) {
            starfallMetrics.cacheHits++;
            if (starfallMetrics.verboseLogging) {
                console.log(`[starfall] ⚡ Starfall data cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        starfallMetrics.cacheMisses++;
        starfallMetrics.databaseQueries++;

        const userData = await getCachedGlobalUser(userId);
        const starfallData = userData.starfall || getDefaultStarfallData();

        // Cache the result
        starfallDataCache.set(cacheKey, starfallData);

        const duration = Date.now() - startTime;
        if (starfallMetrics.verboseLogging || duration > 100) {
            console.log(`[starfall] ✅ Starfall data fetched for ${userId}: ${duration}ms - cached for future access`);
        }

        return starfallData;
    } catch (error) {
        console.error('[starfall] ❌ Error getting starfall data:', error);
        return getDefaultStarfallData();
    }
}

/**
 * Update user's starfall data in database (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced error handling with performance monitoring and cache invalidation
 * @param {string} userId - User ID
 * @param {Object} starfallData - Updated starfall data
 * @returns {Promise<boolean>} Success status
 */
async function updateStarfallData(userId, starfallData) {
    const startTime = Date.now();

    try {
        starfallMetrics.databaseQueries++;

        await optimizedUpdateOne('global_user_data',
            { userId: userId },
            {
                $set: {
                    starfall: starfallData,
                    updatedAt: new Date()
                }
            },
            { upsert: true }
        );

        // OPTIMIZED: Comprehensive cache invalidation
        const cacheKey = `starfall_${userId}`;
        starfallDataCache.delete(cacheKey);

        // Also invalidate related caches
        const claimKey = `claim_${userId}`;
        claimValidationCache.delete(claimKey);

        // Invalidate user cache to ensure fresh data on next read
        const { invalidateUserCache } = require('./globalLevels.js');
        invalidateUserCache(userId);

        const duration = Date.now() - startTime;
        if (starfallMetrics.verboseLogging || duration > 150) {
            console.log(`[starfall] ✅ Starfall data updated for ${userId}: ${duration}ms`);
        }

        return true;
    } catch (error) {
        console.error('[starfall] ❌ Error updating starfall data:', error);
        return false;
    }
}

/**
 * Build dynamic button grid for Starfall interface
 * @param {number} streakDay - Current streak day
 * @param {string} userId - User ID for unique button IDs
 * @param {Object} claimData - Optional claim data for revealed buttons
 * @returns {Array<ActionRowBuilder>} Array of button rows
 */
function buildStarfallButtonGrid(streakDay, userId, claimData = null) {
    const buttonRows = [];
    const totalRows = getRowsForStreak(streakDay);
    const progression = getStreakProgression(streakDay);
    const totalButtons = progression.buttons;

    for (let row = 0; row < totalRows; row++) {
        const buttons = [];
        for (let col = 0; col < 5; col++) {
            const buttonIndex = row * 5 + col;
            if (buttonIndex >= totalButtons) break; // Don't create buttons beyond the limit

            const buttonId = `starfall-claim-${userId}-${row}-${col}`;
            const button = new ButtonBuilder()
                .setCustomId(buttonId);

            // Check if this is the revealed button
            const isRevealed = claimData && claimData.revealedButton === `${row}-${col}`;

            if (claimData && claimData.claimed) {
                // Daily already claimed - disable all buttons
                button.setDisabled(true);

                if (isRevealed) {
                    // This is the revealed button - show the result
                    if (claimData.itemDropped) {
                        // Show item emoji
                        button.setEmoji(claimData.itemEmoji || '🎁')
                              .setStyle(ButtonStyle.Success);
                    } else if (claimData.multiplier > 1) {
                        // Show multiplier as label
                        button.setLabel(`${claimData.multiplier}x`)
                              .setStyle(ButtonStyle.Success);
                    } else {
                        // Show "nothing" emote (using a default for now)
                        button.setEmoji('💫') // Default "nothing" emote
                              .setStyle(ButtonStyle.Secondary);
                    }
                } else {
                    // Other buttons remain blank and disabled
                    button.setEmoji('<:blank:1390825005079859380>')
                          .setStyle(ButtonStyle.Secondary);
                }
            } else {
                // Daily not claimed yet - show blank buttons
                button.setEmoji('<:blank:1390825005079859380>')
                      .setStyle(ButtonStyle.Secondary);
            }

            buttons.push(button);
        }
        if (buttons.length > 0) {
            buttonRows.push(new ActionRowBuilder().addComponents(...buttons));
        }
    }

    return buttonRows;
}

/**
 * Build complete Starfall container interface
 * @param {string} userId - User ID
 * @param {string} statusMessage - Optional status message to display
 * @param {boolean} includeNavigation - Whether to include navigation menu (default: false)
 * @param {string} username - Username for navigation menu (required if includeNavigation is true)
 * @returns {Promise<ContainerBuilder>} Complete Starfall container
 */
async function buildStarfallContainer(userId, statusMessage = null, includeNavigation = false, username = null) {
    try {
        const starfallData = await getStarfallData(userId);

        // FIXED: Check if streak should be reset and update display accordingly
        let currentStreak = starfallData.currentStreak || 0;
        let streakWasReset = false;

        if (shouldResetStreak(starfallData)) {
            currentStreak = 0; // Reset streak for display purposes
            streakWasReset = true;

            // FIXED: Update the actual data to reflect the reset streak
            const updatedStarfallData = {
                ...starfallData,
                currentStreak: 0,
                pendingStreakIncrement: null, // Clear pending increment since streak reset
                todayClaimData: null // Clear today's claim data since streak reset
            };

            // Update database and invalidate cache to ensure consistency
            await updateStarfallData(userId, updatedStarfallData);

            if (starfallMetrics.verboseLogging) {
                console.log(`[starfall] 🔄 Streak reset for user ${userId} due to 48+ hour gap`);
            }
        } else {
            // CRITICAL FIX: Apply pending streak increment if user can claim again (next day)
            const canClaim = canClaimDaily(starfallData, userId);
            if (canClaim && starfallData.pendingStreakIncrement && starfallData.pendingStreakIncrement > currentStreak) {
                currentStreak = starfallData.pendingStreakIncrement;

                // Update database to apply the pending increment
                const updatedStarfallData = {
                    ...starfallData,
                    currentStreak: currentStreak,
                    pendingStreakIncrement: null // Clear pending increment after applying
                };

                await updateStarfallData(userId, updatedStarfallData);

                if (starfallMetrics.verboseLogging) {
                    console.log(`[starfall] ✅ Applied pending streak increment for user ${userId}: ${starfallData.currentStreak} → ${currentStreak}`);
                }
            }
        }

        // CRITICAL FIX: Show pending streak increment immediately after claiming
        let displayStreak, displayStreakText;
        const currentClaimData = getTodayClaimData(starfallData, userId);
        const hasClaimedToday = currentClaimData && currentClaimData.claimed;

        if (userId === process.env.OWNER && ownerTestingMode) {
            // Show what the next streak would be (simulate claiming today)
            const nextStreakForButtons = Math.min(currentStreak + 1, 7); // Cap at day 7 for button progression
            const nextStreakActual = currentStreak + 1; // Actual next streak number
            displayStreak = Math.max(nextStreakForButtons, 1);
            displayStreakText = `${currentStreak} → ${nextStreakActual}`;
        } else if (hasClaimedToday && starfallData.pendingStreakIncrement) {
            // FIXED: Show CURRENT day's button layout after claiming (the new streak that was just achieved)
            // This prevents removing buttons that should still be visible with the reward shown
            displayStreak = Math.max(Math.min(starfallData.pendingStreakIncrement, 7), 1); // Use pending streak (the new streak achieved today)
            displayStreakText = starfallData.pendingStreakIncrement.toString(); // Show the new streak immediately
        } else {
            displayStreak = Math.max(Math.min(currentStreak, 7), 1); // Cap button progression at 7, but use 1 minimum
            displayStreakText = currentStreak.toString(); // Show current streak number
        }

        // Title and description
        const titleDisplay = new TextDisplayBuilder().setContent('# starfall');
        const descDisplay = new TextDisplayBuilder().setContent('> click a button to claim your daily');

        // Stats display - combine streak info and items counter
        const baseStars = getBaseStarsForStreak(displayStreak);

        // Get actual count of starfall items for accurate display
        const starfallItemCount = await optimizedCountDocuments('custom_items', {
            dropLocations: 'STARFALL',
            disabled: { $ne: true },
            guildId: null // Global items only
        });

        // Build stats content with items counter if starfall items exist
        let statsContent = `**daily amount:** ${baseStars}\n**streak:** ${displayStreakText}\n**longest streak:** ${starfallData.longestStreak}`;
        if (starfallItemCount > 0) {
            statsContent += `\n**items:** ${starfallData.itemsFound}/${starfallItemCount}`;
        }

        const statsDisplay = new TextDisplayBuilder().setContent(statsContent);

        // Spacer
        const spacer = new SeparatorBuilder().setSpacing(1).setDivider(false);

        // Status display
        const nextClaimTimestamp = getNextClaimTimestamp(starfallData);
        const canClaim = canClaimDaily(starfallData, userId);

        let statusContent = '';

        if (statusMessage) {
            statusContent += `**status:** ${statusMessage}`;
        } else if (canClaim) {
            // Check if this is bot owner with testing mode enabled
            const isOwnerWithTestingMode = userId === process.env.OWNER && ownerTestingMode && getTodayClaimData(starfallData, userId)?.claimed;
            if (isOwnerWithTestingMode) {
                statusContent += `**status:** 🧪 testing mode - cooldown bypassed`;
            } else {
                statusContent += `**status:** ready to claim!`;
            }
        } else {
            // Show testing mode status for owner when on cooldown
            if (userId === process.env.OWNER) {
                statusContent += `**status:** come back <t:${nextClaimTimestamp}:R> for the next starfall`;
            } else {
                statusContent += `**status:** come back <t:${nextClaimTimestamp}:R> for the next starfall`;
            }
        }

        const statusDisplay = new TextDisplayBuilder().setContent(statusContent);

        // FIXED: Check if daily was already claimed today, but clear if streak was reset
        let todayClaimData = getTodayClaimData(starfallData, userId);
        if (streakWasReset) {
            todayClaimData = null; // Clear today's claim data since streak reset
        }

        // Button grid - show if user can claim OR if they already claimed today (to show result)
        const shouldShowButtons = canClaim || (todayClaimData && todayClaimData.claimed);
        const buttonRows = shouldShowButtons ? buildStarfallButtonGrid(displayStreak, userId, todayClaimData) : [];

        // Build container
        const container = new ContainerBuilder()
            .addTextDisplayComponents(titleDisplay, descDisplay, statsDisplay)
            .addSeparatorComponents(spacer)
            .addTextDisplayComponents(statusDisplay)
            .setAccentColor(OPERATION_COLORS.NEUTRAL);

        // Add button rows if needed
        if (buttonRows.length > 0) {
            container.addActionRowComponents(...buttonRows);
        }

        return container;
    } catch (error) {
        console.error('Error building Starfall container:', error);

        // Fallback container
        const errorDisplay = new TextDisplayBuilder().setContent(
            '# starfall\n\n**status:** error loading starfall data'
        );

        return new ContainerBuilder()
            .addTextDisplayComponents(errorDisplay)
            .setAccentColor(LOG_COLORS.ERROR);
    }
}

/**
 * Process Starfall claim button interaction (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced parallel processing, comprehensive error handling, and performance monitoring
 * @param {Interaction} interaction - Discord interaction
 * @returns {Promise<void>}
 */
async function processStarfallClaim(interaction) {
    const startTime = Date.now();
    starfallMetrics.claimsProcessed++;

    try {
        const userId = interaction.user.id;

        // RACE CONDITION FIX: Check if user already has an active claim operation
        if (activeClaimOperations.has(userId)) {
            console.log(`[starfall] ⚠️ Blocked duplicate claim attempt for user ${userId}`);
            // Return current state without processing
            const starfallData = await getStarfallData(userId);
            const container = await buildStarfallContainer(userId);
            const { buildYouHubMenu } = require('../commands/utility/you.js');
            const hubMenu = await buildYouHubMenu(interaction.user.username, userId, 'daily', true);
            return hubMenu ? [hubMenu, container] : [container];
        }

        // RACE CONDITION FIX: Mark user as having an active operation
        activeClaimOperations.add(userId);

        // OPTIMIZED: Parallel data fetching with comprehensive error handling
        starfallMetrics.parallelOperations++;
        const [starfallDataResult, hubMenuResult] = await Promise.allSettled([
            getStarfallData(userId),
            // Pre-build hub menu for faster response
            (async () => {
                const { buildYouHubMenu } = require('../commands/utility/you.js');
                return buildYouHubMenu(interaction.user.username, userId, 'daily', true);
            })()
        ]);

        const starfallData = starfallDataResult.status === 'fulfilled' ? starfallDataResult.value : getDefaultStarfallData();
        const hubMenu = hubMenuResult.status === 'fulfilled' ? hubMenuResult.value : null;

        // Track partial failures
        const failedOperations = [starfallDataResult, hubMenuResult].filter(result => result.status === 'rejected').length;
        if (failedOperations > 0) {
            starfallMetrics.partialFailures++;
            if (starfallMetrics.verboseLogging) {
                console.warn(`[starfall] ⚠️  ${failedOperations} operations failed for claim ${userId}`);
            }
        }

        // Extract button position from custom ID (starfall-claim-userId-row-col)
        const customIdParts = interaction.customId.split('-');
        const buttonRow = customIdParts[3];
        const buttonCol = customIdParts[4];
        const revealedButton = `${buttonRow}-${buttonCol}`;

        // Check if already claimed today
        const todayClaimData = getTodayClaimData(starfallData, userId);
        if (todayClaimData && todayClaimData.claimed) {
            // Already claimed today - just rebuild container to show current state
            const container = await buildStarfallContainer(userId);
            // CONVERTED: Return components instead of calling interaction.update
            return hubMenu ? [hubMenu, container] : [container];
        }

        // Check if user can claim
        if (!canClaimDaily(starfallData, userId)) {
            const nextClaimTimestamp = getNextClaimTimestamp(starfallData);
            const statusMessage = `⏰ come back <t:${nextClaimTimestamp}:R> for your next reward`;
            const container = await buildStarfallContainer(userId, statusMessage);

            // CONVERTED: Return components instead of calling interaction.update
            return hubMenu ? [hubMenu, container] : [container];
        }

        // CRITICAL FIX: Calculate reward based on next streak but don't update streak until next day
        starfallMetrics.parallelOperations++;
        const [rewardStreakResult, rewardCalculationResult] = await Promise.allSettled([
            // Calculate what the next streak would be (for reward calculation only)
            (async () => {
                if (shouldResetStreak(starfallData)) {
                    return 1; // Reset to day 1
                } else {
                    // Calculate next streak for reward purposes
                    return (starfallData.currentStreak || 0) + 1;
                }
            })(),
            // Pre-calculate reward for caching
            (async () => {
                const cacheKey = `reward_${starfallData.currentStreak || 0}_${shouldResetStreak(starfallData)}`;
                const cached = rewardCalculationCache.get(cacheKey);
                if (cached) {
                    starfallMetrics.cacheHits++;
                    return cached;
                }

                const rewardStreak = shouldResetStreak(starfallData) ? 1 : (starfallData.currentStreak || 0) + 1;
                const reward = calculateStarfallReward(rewardStreak);
                rewardCalculationCache.set(cacheKey, reward);
                return reward;
            })()
        ]);

        const rewardStreak = rewardStreakResult.status === 'fulfilled' ? rewardStreakResult.value : 1;
        const reward = rewardCalculationResult.status === 'fulfilled' ? rewardCalculationResult.value : calculateStarfallReward(rewardStreak);

        // Handle item drop if applicable
        let itemResult = null;
        let itemEmoji = null;
        if (reward.itemDropped) {
            starfallMetrics.itemDropsProcessed++;
            // Pass guild and client for proper notifications and DMs
            itemResult = await processStarfallItemDrop(userId, interaction.guild?.id, interaction.client);
            if (itemResult.success) {
                itemEmoji = itemResult.emoji || itemResult.item?.emoji || '🎁';
            }
        }

        // Create today's claim data for button reveal
        const newClaimData = {
            date: new Date(),
            claimed: true,
            revealedButton: revealedButton,
            multiplier: reward.multiplier,
            itemDropped: reward.itemDropped && itemResult && itemResult.success,
            itemEmoji: itemEmoji
        };

        // CRITICAL FIX: Don't increment streak immediately - store pending increment for next day
        const updatedStarfallData = {
            ...starfallData,
            stars: (starfallData.stars || 0) + reward.stars,
            // CRITICAL FIX: Keep current streak unchanged - it will increment next day
            currentStreak: starfallData.currentStreak || 0,
            // CRITICAL FIX: Store pending streak increment for next day application
            pendingStreakIncrement: shouldResetStreak(starfallData) ? 1 : (starfallData.currentStreak || 0) + 1,
            longestStreak: Math.max(starfallData.longestStreak || 0, rewardStreak),
            lastClaimDate: new Date(),
            totalClaims: (starfallData.totalClaims || 0) + 1,
            itemsFound: (starfallData.itemsFound || 0) + (itemResult && itemResult.success ? 1 : 0),
            todayClaimData: newClaimData
        };

        // Build status message
        let statusMessage = `⭐ **+${reward.stars} stars**`;
        if (reward.multiplier > 1) {
            statusMessage += ` (${reward.baseStars} × ${reward.multiplier})`;
        }

        if (itemResult && itemResult.success) {
            statusMessage += `\n🎁 **${itemResult.item.name}** found!`;
        }

        // Save updated data - CRITICAL: Must succeed to prevent duplicate claims
        const saveSuccess = await updateStarfallData(userId, updatedStarfallData);
        if (!saveSuccess) {
            console.error(`[starfall] ❌ CRITICAL: Failed to save starfall data for user ${userId} - preventing duplicate claims`);
            // RACE CONDITION FIX: Clear active operation before returning error
            activeClaimOperations.delete(userId);

            const errorContainer = await buildStarfallContainer(userId, '❌ error saving reward - please try again');
            return hubMenu ? [hubMenu, errorContainer] : [errorContainer];
        }

        // Build updated container with revealed button
        const container = await buildStarfallContainer(userId, statusMessage);

        // RACE CONDITION FIX: Clear active operation before returning
        activeClaimOperations.delete(userId);

        // CONVERTED: Return components instead of calling interaction.update
        return hubMenu ? [hubMenu, container] : [container];

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        starfallMetrics.averageQueryTime =
            (starfallMetrics.averageQueryTime * (starfallMetrics.claimsProcessed - 1) + duration) /
            starfallMetrics.claimsProcessed;

        if (starfallMetrics.verboseLogging || duration > 200) {
            console.log(`[starfall] ✅ Claim processed for ${userId}: ${duration}ms (${failedOperations} failures)`);
        }

    } catch (error) {
        // RACE CONDITION FIX: Clear active operation on error
        const userId = interaction.user.id;
        activeClaimOperations.delete(userId);

        console.error('[starfall] ❌ Error processing Starfall claim:', error);

        const { buildYouHubMenu } = require('../commands/utility/you.js');
        const errorHubMenu = await buildYouHubMenu(interaction.user.username, interaction.user.id, 'daily', true);
        const errorContainer = await buildStarfallContainer(interaction.user.id, '❌ error processing claim - please try again');

        // CONVERTED: Return components instead of calling interaction.update
        return [errorHubMenu, errorContainer];
    }
}

/**
 * Process Starfall item drop using existing item drop infrastructure
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID where starfall was claimed (for notifications)
 * @param {Object} client - Discord client (for DM notifications)
 * @returns {Promise<Object>} Result object with success status and item data
 */
async function processStarfallItemDrop(userId, guildId = null, client = null) {
    try {
        // Get STARFALL location items (bot-owner-only global items)
        const starfallItems = await optimizedFind('custom_items', {
            dropLocations: 'STARFALL',
            disabled: { $ne: true },
            guildId: null // Global items only
        });

        if (starfallItems.length === 0) {
            console.log('[starfall] No Starfall items available for drop');
            return { success: false, reason: 'no_items' };
        }

        // Use existing performMasterRoll function from itemDrops.js
        const { performMasterRoll } = require('./itemDrops.js');

        // Perform master roll to select item
        const selectedItem = performMasterRoll(starfallItems);
        if (!selectedItem) {
            console.log('[starfall] Master roll returned no item');
            return { success: false, reason: 'no_selection' };
        }

        // Use existing item drop infrastructure for proper integration
        const {
            addItemToInventory,
            addItemDropNotification,
            sendItemDropDM,
            checkFirstItemDropInServer
        } = require('./itemDrops.js');

        // Add item to inventory using existing infrastructure
        // For starfall, use guildId for inventory context (null = global)
        const inventoryGuildId = guildId; // Use actual guild ID for proper inventory categorization
        const droppedItem = await addItemToInventory(userId, inventoryGuildId, selectedItem, 'STARFALL');

        if (!droppedItem) {
            console.error('[starfall] Failed to add item to inventory');
            return { success: false, reason: 'inventory_failed' };
        }

        console.log(`[starfall] ✅ Item dropped: ${droppedItem.itemName} for user ${userId}`);

        // Process notifications and DMs in parallel (don't wait for them)
        const notificationPromises = [];

        // Add to notification queue if guild context is available
        if (guildId) {
            notificationPromises.push(
                addItemDropNotification(userId, guildId, [droppedItem], 'STARFALL').catch(error => {
                    console.error('[starfall] Error adding notification:', error);
                })
            );

            // Check if this is first drop in server for logging
            notificationPromises.push(
                checkFirstItemDropInServer(guildId, selectedItem.id).then(isFirst => {
                    if (isFirst) {
                        console.log(`[starfall] 🎉 First ${droppedItem.itemName} drop in server ${guildId}`);
                    }
                }).catch(error => {
                    console.error('[starfall] Error checking first drop:', error);
                })
            );
        }

        // Send DM notification if client is available
        if (client && guildId) {
            notificationPromises.push(
                sendItemDropDM(userId, guildId, [droppedItem], 'STARFALL', client).then(result => {
                    if (result.success) {
                        console.log(`[starfall] ✅ DM sent to user ${userId} for ${droppedItem.itemName}`);
                    } else {
                        console.log(`[starfall] ❌ DM failed for user ${userId}: ${result.reason || result.error}`);
                    }
                }).catch(error => {
                    console.error('[starfall] Error sending DM:', error);
                })
            );
        }

        // Execute all notifications in parallel without blocking
        Promise.all(notificationPromises);

        return {
            success: true,
            item: droppedItem,
            emoji: droppedItem.itemEmote || '🎁'
        };

    } catch (error) {
        console.error('[starfall] Error processing Starfall item drop:', error);
        return { success: false, reason: 'error', error };
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = starfallMetrics.cacheHits + starfallMetrics.cacheMisses > 0 ?
        (starfallMetrics.cacheHits / (starfallMetrics.cacheHits + starfallMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: starfallMetrics.cacheHits,
            cacheMisses: starfallMetrics.cacheMisses,
            databaseQueries: starfallMetrics.databaseQueries,
            averageQueryTime: `${starfallMetrics.averageQueryTime.toFixed(2)}ms`,
            claimsProcessed: starfallMetrics.claimsProcessed,
            itemDropsProcessed: starfallMetrics.itemDropsProcessed,
            parallelOperations: starfallMetrics.parallelOperations,
            partialFailures: starfallMetrics.partialFailures,
            lastOptimization: new Date(starfallMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            starfallData: starfallDataCache.getStats(),
            rewardCalculation: rewardCalculationCache.getStats(),
            itemDrop: itemDropCache.getStats(),
            claimValidation: claimValidationCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            starfallData: starfallDataCache.getStats().memoryUsage,
            rewardCalculation: rewardCalculationCache.getStats().memoryUsage,
            itemDrop: itemDropCache.getStats().memoryUsage,
            claimValidation: claimValidationCache.getStats().memoryUsage,
            total: starfallDataCache.getStats().memoryUsage +
                   rewardCalculationCache.getStats().memoryUsage +
                   itemDropCache.getStats().memoryUsage +
                   claimValidationCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, starfallMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 150) {
        recommendations.push('Starfall operations are slow - investigate database performance');
    }

    if (metrics.partialFailures > metrics.claimsProcessed * 0.1) {
        recommendations.push('High partial failure rate - investigate system reliability');
    }

    if (metrics.parallelOperations < metrics.claimsProcessed * 0.8) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (metrics.itemDropsProcessed > metrics.claimsProcessed * 0.1) {
        recommendations.push('High item drop rate - monitor item economy balance');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    starfallMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[starfall] 📊 Performance Report:`);
    console.log(`[starfall]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[starfall]   Claims Processed: ${stats.performance.claimsProcessed}`);
    console.log(`[starfall]   Item Drops Processed: ${stats.performance.itemDropsProcessed}`);
    console.log(`[starfall]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[starfall]   Partial Failures: ${stats.performance.partialFailures}`);
    console.log(`[starfall]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[starfall]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[starfall]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[starfall] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all starfall caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllStarfallCaches() {
    starfallDataCache.clear();
    rewardCalculationCache.clear();
    itemDropCache.clear();
    claimValidationCache.clear();

    console.log('[starfall] 🗑️ Cleared all starfall caches');
}

/**
 * Invalidate specific user starfall caches
 * @param {string} userId - User ID
 */
function invalidateUserStarfallCaches(userId) {
    const keys = [
        `starfall_${userId}`,
        `claim_${userId}`
    ];

    let invalidatedCount = 0;
    keys.forEach(key => {
        if (starfallDataCache.delete(key)) invalidatedCount++;
        if (claimValidationCache.delete(key)) invalidatedCount++;
    });

    // Also clear reward calculation cache entries that might be related
    const rewardKeys = Array.from(rewardCalculationCache.keys()).filter(key => key.includes(userId));
    rewardKeys.forEach(key => {
        if (rewardCalculationCache.delete(key)) invalidatedCount++;
    });

    if (starfallMetrics.verboseLogging) {
        console.log(`[starfall] 🗑️ Invalidated ${invalidatedCount} cache entries for user ${userId}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, starfallMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getStreakProgression,
    getBaseStarsForStreak,
    getRowsForStreak,
    getButtonsForStreak,
    rollMultiplier,
    getItemDropChance,
    rollItemDrop,
    calculateStarfallReward,
    canClaimDaily,
    shouldResetStreak,
    getTodayClaimData,
    getStarfallMenuDescription,
    getNextClaimTimestamp,
    getDefaultStarfallData,
    getStarfallData,
    updateStarfallData,
    buildStarfallButtonGrid,
    buildStarfallContainer,
    processStarfallClaim,
    processStarfallItemDrop,
    setOwnerTestingMode,
    getOwnerTestingMode,

    // Enhanced optimization functions
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllStarfallCaches,
    invalidateUserStarfallCaches,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...starfallMetrics }),

    // Constants
    STREAK_PROGRESSION,
    MULTIPLIER_ODDS,
    ITEM_DROP_CHANCES
};
