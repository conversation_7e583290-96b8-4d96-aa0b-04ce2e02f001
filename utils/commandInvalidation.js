const { mongoClient } = require('../mongo/client.js');
const { optimizedDeleteMany, optimizedInsertOne, optimizedFindOne } = require('./database-optimizer.js');
const { Action<PERSON>ow<PERSON>uilder, ButtonBuilder, StringSelectMenuBuilder, ButtonStyle } = require('discord.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

/**
 * Command Invalidation System
 * Tracks active slash commands per user and disables previous interactions
 * to prevent spam, rate limiting, and confusion
 */

// In-memory cache for active commands (faster than DB for this use case)
const activeCommands = new Map(); // userId -> { commandName, interactionId, timestamp }

// LRU Cache for invalidation checks to reduce database queries with automatic memory management
const invalidationCheckCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes TTL
registerCache(invalidationCheckCache);

/**
 * Register a new slash command for a user and invalidate previous ones of the same type
 * @param {string} userId - User ID
 * @param {string} commandName - Command name (17, you, etc.)
 * @param {string} interactionId - Interaction ID
 * @param {Object} client - Discord client
 */
async function registerCommand(userId, commandName, interactionId, client) {
    try {
        const previousCommand = activeCommands.get(userId);

        // Only invalidate if user has a previous command of the SAME TYPE
        if (previousCommand &&
            previousCommand.interactionId !== interactionId &&
            previousCommand.commandName === commandName) {
            console.log(`[commandInvalidation] Invalidating previous ${commandName} command for user ${userId}`);
            await invalidatePreviousCommand(previousCommand, client);
        }

        // Register the new command
        activeCommands.set(userId, {
            userId,
            commandName,
            interactionId,
            timestamp: Date.now()
        });

        // CRITICAL FIX: Clear any invalidated command entries for this user/command type
        // when starting a new active session to prevent blocking component interactions
        await optimizedDeleteMany('invalidated_commands', {
            userId: userId,
            commandName: commandName
        });

        // Also clear the LRU cache entry to prevent cached invalidation results
        const cacheKey = `${userId}_${commandName}`;
        invalidationCheckCache.delete(cacheKey);

        console.log(`[commandInvalidation] Registered new command for user ${userId}: ${commandName} (cleared any previous invalidated entries)`);

    } catch (error) {
        console.error('[commandInvalidation] Error registering command:', error);
    }
}

/**
 * Invalidate a previous command by disabling all its components
 * @param {Object} commandInfo - Previous command info
 * @param {Object} client - Discord client
 */
async function invalidatePreviousCommand(commandInfo, client) {
    try {
        // Store invalidated commands in the database and check on component interactions

        // Remove any existing invalidated commands for this user and command type
        await optimizedDeleteMany('invalidated_commands', {
            userId: commandInfo.userId,
            commandName: commandInfo.commandName
        });

        // Add the new invalidated command
        await optimizedInsertOne('invalidated_commands', {
            userId: commandInfo.userId,
            commandName: commandInfo.commandName,
            interactionId: commandInfo.interactionId,
            invalidatedAt: new Date(),
            timestamp: commandInfo.timestamp
        });

        console.log(`[commandInvalidation] Marked ${commandInfo.commandName} command as invalidated: ${commandInfo.interactionId}`);

    } catch (error) {
        console.error('[commandInvalidation] Error invalidating previous command:', error);
    }
}

/**
 * Check if an interaction is from an invalidated command
 * @param {Object} interaction - Discord interaction
 * @returns {boolean} True if the interaction should be blocked
 */
async function isInteractionInvalidated(interaction) {
    try {
        // Only check for component interactions (buttons, select menus)
        if (!interaction.isButton() && !interaction.isStringSelectMenu() && !interaction.isChannelSelectMenu()) {
            return false;
        }

        // Get the command type from the interaction customId
        let commandType = null;
        if (interaction.customId.startsWith('you-')) {
            commandType = 'you';
        } else if (interaction.customId.startsWith('17-') || interaction.customId.startsWith('owner-') ||
                   interaction.customId.startsWith('items-') || interaction.customId.startsWith('exp-') ||
                   interaction.customId.startsWith('logs-') || interaction.customId.startsWith('opener-') ||
                   interaction.customId.startsWith('dehoist-') || interaction.customId.startsWith('sticky-')) {
            commandType = '17';
        }

        if (!commandType) {
            // Unknown command type, allow interaction
            return false;
        }

        // Check if this user has an active command of the same type
        const activeCommand = activeCommands.get(interaction.user.id);

        if (!activeCommand || activeCommand.commandName !== commandType) {
            // PERFORMANCE FIX: Use LRU cache to avoid repeated database queries
            const cacheKey = `${interaction.user.id}_${commandType}`;

            // Check LRU cache first (handles TTL automatically)
            let cachedResult = invalidationCheckCache.get(cacheKey);

            if (cachedResult === undefined) {
                // Cache miss - query database
                const invalidatedCommand = await optimizedFindOne('invalidated_commands', {
                    userId: interaction.user.id,
                    commandName: commandType
                });

                // Cache the result (LRU handles eviction and TTL automatically)
                cachedResult = !!invalidatedCommand;
                invalidationCheckCache.set(cacheKey, cachedResult);

                if (invalidatedCommand) {
                    console.log(`[commandInvalidation] Blocking interaction from invalidated ${commandType} command (no active command): ${interaction.customId}`);
                    return true;
                }
            } else {
                // Cache hit - use cached result
                if (cachedResult) {
                    console.log(`[commandInvalidation] Blocking interaction from cached invalidated ${commandType} command: ${interaction.customId}`);
                    return true;
                }
            }

            // No active or invalidated command, allow interaction
            return false;
        }

        // There is an active command of this type, allow all interactions from current session
        // This is the key fix - if there's an active command, always allow interactions
        return false;

    } catch (error) {
        console.error('[commandInvalidation] Error checking interaction invalidation:', error);
        return false; // Don't block on error
    }
}

/**
 * Disable all components in an ActionRow
 * @param {ActionRowBuilder} actionRow - Action row to disable
 * @returns {ActionRowBuilder} Disabled action row
 */
function disableActionRow(actionRow) {
    if (!actionRow || !actionRow.components) return actionRow;
    
    const disabledComponents = actionRow.components.map(component => {
        if (component instanceof ButtonBuilder) {
            return ButtonBuilder.from(component).setDisabled(true);
        } else if (component instanceof StringSelectMenuBuilder) {
            return StringSelectMenuBuilder.from(component).setDisabled(true);
        }
        return component;
    });
    
    return new ActionRowBuilder().addComponents(disabledComponents);
}

/**
 * Disable all components in a components array
 * @param {Array} components - Array of components to disable
 * @returns {Array} Array with disabled components
 */
function disableAllComponents(components) {
    if (!Array.isArray(components)) return components;
    
    return components.map(component => {
        if (component instanceof ActionRowBuilder) {
            return disableActionRow(component);
        }
        return component;
    });
}

/**
 * Clean up old invalidated commands (run periodically)
 */
async function cleanupInvalidatedCommands() {
    try {
        const col = mongoClient.db('seventeen_bot').collection('invalidated_commands');

        // Remove commands invalidated more than 30 minutes ago
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

        const result = await optimizedDeleteMany('invalidated_commands', {
            invalidatedAt: { $lt: thirtyMinutesAgo }
        });

        if (result.deletedCount > 0) {
            console.log(`[commandInvalidation] Cleaned up ${result.deletedCount} old invalidated commands`);
        }

        // Clean up active commands cache (LRU cache cleans itself automatically)
        const currentTime = Date.now();
        for (const [userId, commandInfo] of activeCommands.entries()) {
            if (currentTime - commandInfo.timestamp > 30 * 60 * 1000) { // 30 minutes
                activeCommands.delete(userId);
            }
        }

        // Note: invalidationCheckCache (LRU) handles its own cleanup automatically

    } catch (error) {
        console.error('[commandInvalidation] Error cleaning up invalidated commands:', error);
    }
}

/**
 * Create database indexes for optimal performance
 * Call this once during bot startup
 */
async function createInvalidationIndexes() {
    try {
        const col = mongoClient.db('seventeen_bot').collection('invalidated_commands');

        // Create compound index for the most common query pattern
        await col.createIndex(
            { userId: 1, commandName: 1 },
            { name: 'userId_commandName', background: true }
        );

        // Create index for cleanup operations
        await col.createIndex(
            { invalidatedAt: 1 },
            { name: 'invalidatedAt', background: true }
        );

        console.log('[commandInvalidation] ✅ Created performance indexes for invalidated_commands collection');

    } catch (error) {
        if (error.code === 85) {
            // Index already exists, ignore
            console.log('[commandInvalidation] ℹ️ Invalidation indexes already exist');
        } else {
            console.error('[commandInvalidation] ❌ Error creating invalidation indexes:', error);
        }
    }
}

// Run cleanup every 30 minutes
setInterval(cleanupInvalidatedCommands, 30 * 60 * 1000);

// Create indexes on startup (with delay to ensure MongoDB is connected)
setTimeout(createInvalidationIndexes, 5000);

module.exports = {
    registerCommand,
    isInteractionInvalidated,
    disableAllComponents,
    disableActionRow,
    cleanupInvalidatedCommands,
    createInvalidationIndexes
};
