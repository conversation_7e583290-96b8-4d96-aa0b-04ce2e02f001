# Discord.js 14.21.0 Methods Reference Guide

**Comprehensive reference for Discord.js Components v2 patterns and architectural standards used in the bot codebase.**

---

## 📋 **Table of Contents**

1. [Import Patterns](#import-patterns)
2. [Container System](#container-system)
3. [Text Display Components](#text-display-components)
4. [Thumbnail & Section Components](#thumbnail--section-components)
5. [Interactive Components](#interactive-components)
6. [Modal Components](#modal-components)
7. [Color System Integration](#color-system-integration)
8. [Universal Interaction Manager](#universal-interaction-manager)
9. [Error Handling Patterns](#error-handling-patterns)
10. [Best Practices & Standards](#best-practices--standards)

---

## 🔧 **Import Patterns**

### **Standard Discord.js Imports**
```javascript
// Core UI Components
const { 
    ContainerBuilder, 
    SectionBuilder, 
    TextDisplayBuilder,
    ThumbnailBuilder,
    SeparatorBuilder,
    SeparatorSpacingSize
} = require('discord.js');

// Interactive Components
const { 
    ActionRowBuilder, 
    ButtonBuilder, 
    ButtonStyle,
    StringSelectMenuBuilder,
    ChannelSelectMenuBuilder,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle
} = require('discord.js');

// System Components
const { 
    MessageFlags,
    PermissionFlagsBits,
    ApplicationCommandType,
    ContextMenuCommandBuilder
} = require('discord.js');
```

### **Bot-Specific Imports**
```javascript
// Color System (MANDATORY)
const { OPERATION_COLORS, LOG_COLORS, RARITY_COLORS } = require('../../utils/colors.js');

// Universal Interaction Manager (MANDATORY)
const { handleUIOperation } = require('../../utils/interactionManager.js');

// Database Optimization
const { optimizedFindOne, optimizedUpdateOne } = require('../../utils/database-optimizer.js');
```

---

## 🏗️ **Container System**

### **ContainerBuilder - Primary UI Container**
```javascript
// Basic Container
const container = new ContainerBuilder()
    .addTextDisplayComponents(titleDisplay, contentDisplay)
    .setAccentColor(OPERATION_COLORS.NEUTRAL);

// Container with Multiple Component Types
const container = new ContainerBuilder()
    .addSectionComponents(headerSection)
    .addTextDisplayComponents(infoDisplay, statusDisplay)
    .addSeparatorComponents(separator)
    .addActionRowComponents(buttonRow)
    .setAccentColor(LOG_COLORS.SUCCESS);

// Dynamic Component Addition
components.forEach(component => {
    if (component instanceof SectionBuilder) {
        container.addSectionComponents(component);
    } else if (component instanceof TextDisplayBuilder) {
        container.addTextDisplayComponents(component);
    } else if (component instanceof ActionRowBuilder) {
        container.addActionRowComponents(component);
    }
});
```

### **Container Color Patterns**
```javascript
// Operation-based Colors
.setAccentColor(OPERATION_COLORS.ADD)      // Green - Create operations
.setAccentColor(OPERATION_COLORS.EDIT)     // Orange - Edit operations  
.setAccentColor(OPERATION_COLORS.DELETE)   // Red - Delete/Error operations
.setAccentColor(OPERATION_COLORS.NEUTRAL)  // Purple - Default/Neutral
.setAccentColor(OPERATION_COLORS.ENTITY)   // Yellow - Main entities (/17, /you)

// Status-based Colors
.setAccentColor(LOG_COLORS.SUCCESS)        // Green - Success states
.setAccentColor(LOG_COLORS.ERROR)          // Red - Error states
.setAccentColor(LOG_COLORS.WARNING)        // Orange - Warning states
.setAccentColor(LOG_COLORS.INFO)           // Blue - Info states
.setAccentColor(LOG_COLORS.PREMIUM)        // Gold - Premium features
```

---

## 📝 **Text Display Components**

### **TextDisplayBuilder - Content Display**
```javascript
// Basic Text Display
const titleDisplay = new TextDisplayBuilder()
    .setContent('# Main Title');

const contentDisplay = new TextDisplayBuilder()
    .setContent('**field**: value\n**another field**: another value');

// Markdown Formatting Patterns
const heading = new TextDisplayBuilder().setContent('# Large Heading');
const subheading = new TextDisplayBuilder().setContent('## Medium Heading');
const quote = new TextDisplayBuilder().setContent('> Quoted text content');
const bold = new TextDisplayBuilder().setContent('**bold text**');
const code = new TextDisplayBuilder().setContent('`inline code`');

// Status Message Pattern (STANDARD)
const statusDisplay = new TextDisplayBuilder()
    .setContent('**status:** ✅ Operation completed successfully');

const errorDisplay = new TextDisplayBuilder()
    .setContent('**status:** ❌ An error occurred');
```

### **Content Splitting for Long Text**
```javascript
// Utility function for splitting long content
function createTextDisplays(content, prefix = '') {
    const chunks = splitTextContent(content); // Max 2000 chars per chunk
    return chunks.map((chunk, index) => {
        let displayContent = chunk;
        if (prefix && index === 0) {
            displayContent = `${prefix}\n${chunk}`;
        } else if (prefix) {
            displayContent = `${prefix} (continued)\n${chunk}`;
        }
        return new TextDisplayBuilder().setContent(displayContent);
    });
}

// Usage
const longContentDisplays = createTextDisplays(longText, '## Content Section');
container.addTextDisplayComponents(...longContentDisplays);
```

---

## 🖼️ **Thumbnail & Section Components**

### **ThumbnailBuilder - Image Display**
```javascript
// User Avatar Thumbnail
const profileImage = new ThumbnailBuilder({
    media: { url: user.displayAvatarURL({ forceStatic: false }) }
});

// Custom Image Thumbnail
const customImage = new ThumbnailBuilder({
    media: { url: 'https://example.com/image.png' }
});

// Bot Avatar Thumbnail
const botImage = new ThumbnailBuilder({
    media: { url: interaction.client.user.displayAvatarURL({ forceStatic: false }) }
});
```

### **SectionBuilder - Header Sections with Thumbnails**
```javascript
// Section with Thumbnail Accessory
const titleText = `## ${roleIcon} ${roleName} | ${levelIcon} ${levelName}`;
const quoteText = `you in ${guildName} and globally`;

const titleSection = new SectionBuilder()
    .setThumbnailAccessory(profileImage)
    .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(titleText),
        new TextDisplayBuilder().setContent(`> ${quoteText}`)
    );

// Section without Thumbnail
const infoSection = new SectionBuilder()
    .addTextDisplayComponents(
        new TextDisplayBuilder().setContent('## Information'),
        new TextDisplayBuilder().setContent('**details**: content here')
    );
```

### **SeparatorBuilder - Visual Spacing**
```javascript
// Small Separator (no divider line)
const smallSeparator = new SeparatorBuilder()
    .setSpacing(SeparatorSpacingSize.Small)
    .setDivider(false);

// Medium Separator with Divider
const mediumSeparator = new SeparatorBuilder()
    .setSpacing(SeparatorSpacingSize.Medium)
    .setDivider(true);

// Large Separator
const largeSeparator = new SeparatorBuilder()
    .setSpacing(SeparatorSpacingSize.Large)
    .setDivider(true);

// Usage
container.addSeparatorComponents(smallSeparator);
```

---

## 🎛️ **Interactive Components**

### **ButtonBuilder - Action Buttons**
```javascript
// Button Styles
const primaryButton = new ButtonBuilder()
    .setCustomId('action-primary')
    .setLabel('Primary Action')
    .setStyle(ButtonStyle.Primary)
    .setEmoji('✅');

const secondaryButton = new ButtonBuilder()
    .setCustomId('action-secondary')
    .setLabel('Secondary')
    .setStyle(ButtonStyle.Secondary);

const successButton = new ButtonBuilder()
    .setCustomId('action-success')
    .setLabel('Confirm')
    .setStyle(ButtonStyle.Success)
    .setEmoji('✅');

const dangerButton = new ButtonBuilder()
    .setCustomId('action-danger')
    .setLabel('Delete')
    .setStyle(ButtonStyle.Danger)
    .setEmoji('🗑️');

const linkButton = new ButtonBuilder()
    .setURL('https://example.com')
    .setLabel('External Link')
    .setStyle(ButtonStyle.Link);

// Disabled Button
const disabledButton = new ButtonBuilder()
    .setCustomId('action-disabled')
    .setLabel('Unavailable')
    .setStyle(ButtonStyle.Secondary)
    .setDisabled(true);

// Button Row
const buttonRow = new ActionRowBuilder()
    .addComponents(primaryButton, secondaryButton, dangerButton);
```

### **StringSelectMenuBuilder - Dropdown Menus**
```javascript
// Basic Select Menu
const selectMenu = new StringSelectMenuBuilder()
    .setCustomId('feature-select')
    .setPlaceholder('choose an option')
    .addOptions([
        {
            label: 'Option 1',
            value: 'option1',
            description: 'First option description',
            emoji: '1️⃣'
        },
        {
            label: 'Option 2', 
            value: 'option2',
            description: 'Second option description',
            emoji: '2️⃣'
        }
    ]);

// Dynamic Options with Filtering (Standard Pattern)
const options = [];
if (currentSelection !== 'option1') {
    options.push({
        label: 'Option 1',
        value: 'option1',
        description: 'Available option'
    });
}

// Clean Placeholder Text (Remove Dashes)
let placeholderText = currentFeature || 'choose feature';
if (placeholderText === 'text-stats') {
    placeholderText = 'text stats';
}

const dynamicMenu = new StringSelectMenuBuilder()
    .setCustomId('dynamic-select')
    .setPlaceholder(placeholderText)
    .addOptions(options);

const selectRow = new ActionRowBuilder().addComponents(selectMenu);
```

### **ChannelSelectMenuBuilder - Channel Selection**
```javascript
const channelSelect = new ChannelSelectMenuBuilder()
    .setCustomId('channel-select')
    .setPlaceholder('select a channel')
    .setChannelTypes([ChannelType.GuildText, ChannelType.GuildVoice]);

const channelRow = new ActionRowBuilder().addComponents(channelSelect);
```

---

## 📋 **Modal Components**

### **ModalBuilder - Input Forms**
```javascript
// Basic Modal
const modal = new ModalBuilder()
    .setCustomId('input-modal')
    .setTitle('Input Form');

// Text Input Components
const shortInput = new TextInputBuilder()
    .setCustomId('short-input')
    .setLabel('Short Text')
    .setStyle(TextInputStyle.Short)
    .setPlaceholder('Enter short text here')
    .setRequired(true)
    .setMaxLength(100);

const longInput = new TextInputBuilder()
    .setCustomId('long-input')
    .setLabel('Long Text')
    .setStyle(TextInputStyle.Paragraph)
    .setPlaceholder('Enter longer text here')
    .setRequired(false)
    .setMaxLength(1000);

// Pre-populate with existing value
if (existingValue) {
    shortInput.setValue(existingValue);
}

// Add to Modal
const shortRow = new ActionRowBuilder().addComponents(shortInput);
const longRow = new ActionRowBuilder().addComponents(longInput);
modal.addComponents(shortRow, longRow);

// Show Modal (Universal Interaction Manager Compatible)
await interaction.showModal(modal);
return; // No components to return when showing modal
```

---

## 🎨 **Color System Integration**

### **Centralized Color Constants**
```javascript
// MANDATORY: Always import from centralized system
const { OPERATION_COLORS, LOG_COLORS, RARITY_COLORS } = require('../../utils/colors.js');

// ❌ NEVER do this
container.setAccentColor(0x69FF69);

// ✅ ALWAYS do this  
container.setAccentColor(OPERATION_COLORS.ADD);
```

### **Color Reference Values**
```javascript
// Operation Colors (UI Actions)
OPERATION_COLORS.ADD      // 0x69FF69 - Green
OPERATION_COLORS.EDIT     // 0xFFB469 - Orange  
OPERATION_COLORS.DELETE   // 0xff6969 - Red
OPERATION_COLORS.NEUTRAL  // 0x6969ff - Purple
OPERATION_COLORS.ENTITY   // 0xffff69 - Yellow

// Status Colors (Feedback)
LOG_COLORS.SUCCESS        // 0x00D166 - Green
LOG_COLORS.ERROR          // 0xED4245 - Red
LOG_COLORS.WARNING        // 0xF39C12 - Orange
LOG_COLORS.INFO           // 0x5865F2 - Blurple
LOG_COLORS.DISABLED       // 0x95A5A6 - Gray
LOG_COLORS.PREMIUM        // 0xF1C40F - Gold

// Item Rarity Colors (Items System Only)
RARITY_COLORS.COMMON      // 0xA9A9A9 - Gray
RARITY_COLORS.UNCOMMON    // 0x7FCF6B - Light Green
RARITY_COLORS.RARE        // 0x4b69ff - Blue
RARITY_COLORS.MYTHICAL    // 0x8847ff - Purple
RARITY_COLORS.GALACTIC    // 0xeb4b4b - Red
RARITY_COLORS.UNKNOWN     // 0xFF46E3 - Pink
```

---

## ⚡ **Universal Interaction Manager**

### **Standard Handler Patterns**
```javascript
// Execute Handler (Slash Commands)
async function execute(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        // Command logic here
        const container = new ContainerBuilder()
            .addTextDisplayComponents(titleDisplay)
            .setAccentColor(OPERATION_COLORS.NEUTRAL);
        
        return [container];
    }, {
        autoDefer: true,    // Auto-defer for potentially slow operations
        ephemeral: true,    // Private response
        fallbackMessage: '❌ Command failed. Please try again.'
    });
}

// Button Handler
async function buttons(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        // Button logic here
        return [updatedContainer];
    }, {
        autoDefer: false,   // Fast operations don't need deferring
        ephemeral: true,
        fallbackMessage: '❌ Button interaction failed.'
    });
}

// Select Menu Handler  
async function select(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        // Selection logic here
        return [newContainer, buttonRow];
    }, {
        autoDefer: false,   // Select menus are typically fast
        ephemeral: true,
        fallbackMessage: '❌ Selection failed.'
    });
}

// Modal Submit Handler
async function modalSubmit(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        // Process modal input
        const inputValue = interaction.fields.getTextInputValue('input-id');
        return [resultContainer];
    }, {
        autoDefer: false,   // Modal processing is usually fast
        ephemeral: true,
        fallbackMessage: '❌ Form submission failed.'
    });
}
```

### **Configuration Guidelines**
```javascript
// autoDefer Settings
autoDefer: true     // Use for: execute functions, database operations, API calls
autoDefer: false    // Use for: buttons, selects, modals, fast operations

// ephemeral Settings  
ephemeral: true     // Use for: admin commands, error messages, temporary responses
ephemeral: false    // Use for: public commands, shared content

// fallbackMessage Examples
fallbackMessage: '❌ Command failed. Please try again.'
fallbackMessage: '❌ Button interaction failed. Please try again.'
fallbackMessage: '❌ Selection failed. Please try again.'
fallbackMessage: '❌ Form submission failed. Please try again.'
```

---

## 🚨 **Error Handling Patterns**

### **Standard Error Container Pattern**
```javascript
// Basic Error Container
const errorContainer = new ContainerBuilder()
    .addTextDisplayComponents(new TextDisplayBuilder()
        .setContent('**status:** ❌ An error occurred'))
    .setAccentColor(OPERATION_COLORS.DELETE);

// Detailed Error Container
const detailedErrorContainer = new ContainerBuilder()
    .addTextDisplayComponents(
        new TextDisplayBuilder().setContent('## Error'),
        new TextDisplayBuilder().setContent('**status:** ❌ Specific error message here'),
        new TextDisplayBuilder().setContent('**suggestion**: Try refreshing or contact support')
    )
    .setAccentColor(OPERATION_COLORS.DELETE);

// Permission Error Pattern
const permissionErrorContainer = new ContainerBuilder()
    .addTextDisplayComponents(new TextDisplayBuilder()
        .setContent('**status:** ❌ Access denied. Administrator permissions required.'))
    .setAccentColor(OPERATION_COLORS.DELETE);

// Return Error Container (NEVER use direct interaction replies)
return [errorContainer];
```

### **Try-Catch Error Handling**
```javascript
return handleUIOperation(interaction, async (interaction) => {
    try {
        // Main operation logic
        const result = await performOperation();
        
        const successContainer = new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder()
                .setContent('**status:** ✅ Operation completed successfully'))
            .setAccentColor(LOG_COLORS.SUCCESS);
            
        return [successContainer];
        
    } catch (error) {
        console.error('[command] Operation failed:', error);
        
        const errorContainer = new ContainerBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder()
                .setContent('**status:** ❌ Operation failed. Please try again.'))
            .setAccentColor(OPERATION_COLORS.DELETE);
            
        return [errorContainer];
    }
}, {
    autoDefer: true,
    ephemeral: true,
    fallbackMessage: '❌ An unexpected error occurred.'
});
```

---

## 📏 **Best Practices & Standards**

### **UI/UX Standards**
- **Single-page interfaces** with cascading select menus (no multi-page flows)
- **Hide unavailable options** from select menus instead of showing disabled options
- **Use thumbnail sections** for titles with user avatars
- **Status messages** use `**status:** [message]` format in containers
- **Clean placeholder text** without hyphens or underscores

### **Components v2 Compliance**
```javascript
// ✅ CORRECT: Components v2 Pattern
return [container, buttonRow];

// ❌ INCORRECT: Legacy content field with Components v2
await interaction.editReply({
    content: 'message',  // ❌ Cannot use with MessageFlags.IsComponentsV2
    components: [container],
    flags: MessageFlags.IsComponentsV2
});

// ✅ CORRECT: Components v2 Only
await interaction.editReply({
    components: [container],
    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral
});
```

### **Permission Patterns**
```javascript
// Context Menu Commands
.setDefaultMemberPermissions(PermissionFlagsBits.Administrator)

// Application Command Types
ApplicationCommandType.ChatInput    // Slash commands
ApplicationCommandType.Message      // Message context menus  
ApplicationCommandType.User         // User context menus

// Owner-only Check Pattern
if (interaction.user.id !== process.env.OWNER) {
    const accessDeniedContainer = new ContainerBuilder()
        .addTextDisplayComponents(new TextDisplayBuilder()
            .setContent('**status:** ❌ Access denied. Owner only.'))
        .setAccentColor(OPERATION_COLORS.DELETE);
    return [accessDeniedContainer];
}
```

### **Message Flags Usage**
```javascript
// Standard Flags Combination
MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral

// Individual Flags
MessageFlags.IsComponentsV2    // Required for Components v2
MessageFlags.Ephemeral         // Private response
MessageFlags.SuppressEmbeds    // Suppress link previews
```

### **Performance Considerations**
- **Cache frequently used data** with LRUCache system
- **Use optimized database functions** from database-optimizer.js
- **Batch database operations** when possible
- **Implement proper error boundaries** to prevent cascading failures
- **Use autoDefer: true** for operations that might take >1 second

---

## 🎯 **Quick Reference Checklist**

### **Before Creating New Commands:**
- [ ] Import Discord.js components correctly
- [ ] Import centralized color system
- [ ] Import Universal Interaction Manager
- [ ] Plan container structure and color scheme

### **During Development:**
- [ ] Use ContainerBuilder for all UI elements
- [ ] Apply appropriate accent colors from color system
- [ ] Implement proper error handling with error containers
- [ ] Use handleUIOperation for all interaction handlers
- [ ] Follow Components v2 compliance patterns

### **Before Deployment:**
- [ ] Test syntax with `node --check filename.js`
- [ ] Verify all imports are correct
- [ ] Ensure error handling covers all edge cases
- [ ] Test with both success and failure scenarios
- [ ] Validate color usage follows standards

---

---

## 🔍 **Advanced Patterns & Examples**

### **Complex Container Compositions**
```javascript
// Multi-Section Container with Various Components
async function buildComplexContainer(data) {
    const container = new ContainerBuilder();

    // Header Section with Thumbnail
    const headerSection = new SectionBuilder()
        .setThumbnailAccessory(new ThumbnailBuilder({
            media: { url: data.avatarUrl }
        }))
        .addTextDisplayComponents(
            new TextDisplayBuilder().setContent(`## ${data.title}`),
            new TextDisplayBuilder().setContent(`> ${data.subtitle}`)
        );

    // Information Displays
    const infoDisplay = new TextDisplayBuilder()
        .setContent(`**level**: ${data.level}\n**experience**: ${data.exp.toLocaleString()}`);

    const statsDisplay = new TextDisplayBuilder()
        .setContent(`**messages**: ${data.messages}\n**voice time**: ${data.voiceTime}`);

    // Separator
    const separator = new SeparatorBuilder()
        .setSpacing(SeparatorSpacingSize.Medium)
        .setDivider(true);

    // Action Buttons
    const actionButton = new ButtonBuilder()
        .setCustomId('action-button')
        .setLabel('Take Action')
        .setStyle(ButtonStyle.Primary);

    const buttonRow = new ActionRowBuilder().addComponents(actionButton);

    // Assemble Container
    container
        .addSectionComponents(headerSection)
        .addTextDisplayComponents(infoDisplay, statsDisplay)
        .addSeparatorComponents(separator)
        .addActionRowComponents(buttonRow)
        .setAccentColor(OPERATION_COLORS.ENTITY);

    return container;
}
```

### **Dynamic Content Generation**
```javascript
// Dynamic Select Menu Options
function buildDynamicSelectMenu(availableOptions, currentSelection) {
    const options = [];

    // Filter out current selection and add available options
    availableOptions.forEach(option => {
        if (option.value !== currentSelection) {
            options.push({
                label: option.label,
                value: option.value,
                description: option.description,
                emoji: option.emoji || null
            });
        }
    });

    // Add back option if not on main page
    if (currentSelection !== 'main') {
        options.push({
            label: 'back to main',
            value: 'main',
            description: 'return to main menu',
            emoji: '🔙'
        });
    }

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('dynamic-menu')
        .setPlaceholder(getCleanPlaceholder(currentSelection))
        .addOptions(options);

    return new ActionRowBuilder().addComponents(selectMenu);
}

// Clean placeholder text helper
function getCleanPlaceholder(selection) {
    const cleanMap = {
        'text-stats': 'text stats',
        'voice-stats': 'voice stats',
        'user-settings': 'user settings'
    };
    return cleanMap[selection] || selection || 'choose option';
}
```

### **Conditional Component Rendering**
```javascript
// Conditional UI Components Based on Permissions/State
function buildConditionalContainer(user, hasPermission, isEnabled) {
    const components = [];

    // Always show title
    components.push(
        new TextDisplayBuilder().setContent(`## Feature: ${isEnabled ? 'Enabled' : 'Disabled'}`)
    );

    // Show status based on state
    if (isEnabled) {
        components.push(
            new TextDisplayBuilder().setContent('**status**: ✅ Feature is active')
        );
    } else {
        components.push(
            new TextDisplayBuilder().setContent('**status**: ❌ Feature is disabled')
        );
    }

    // Permission-based content
    if (hasPermission) {
        components.push(
            new TextDisplayBuilder().setContent('**controls**: Available below')
        );

        // Add control buttons
        const toggleButton = new ButtonBuilder()
            .setCustomId('toggle-feature')
            .setLabel(isEnabled ? 'Disable' : 'Enable')
            .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success);

        const buttonRow = new ActionRowBuilder().addComponents(toggleButton);

        const container = new ContainerBuilder()
            .addTextDisplayComponents(...components)
            .addActionRowComponents(buttonRow)
            .setAccentColor(isEnabled ? LOG_COLORS.SUCCESS : LOG_COLORS.DISABLED);

        return container;
    } else {
        // No permission - show demo or restricted message
        components.push(
            new TextDisplayBuilder().setContent('**access**: Contact administrator for access')
        );

        const container = new ContainerBuilder()
            .addTextDisplayComponents(...components)
            .setAccentColor(LOG_COLORS.DISABLED);

        return container;
    }
}
```

### **Form Validation Patterns**
```javascript
// Modal Input Validation
async function handleModalSubmit(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        const inputValue = interaction.fields.getTextInputValue('user-input');

        // Validation
        const validation = validateInput(inputValue);
        if (!validation.isValid) {
            const errorContainer = new ContainerBuilder()
                .addTextDisplayComponents(
                    new TextDisplayBuilder().setContent('## Validation Error'),
                    new TextDisplayBuilder().setContent(`**status**: ❌ ${validation.error}`),
                    new TextDisplayBuilder().setContent(`**suggestion**: ${validation.suggestion}`)
                )
                .setAccentColor(OPERATION_COLORS.DELETE);

            return [errorContainer];
        }

        // Process valid input
        const result = await processInput(inputValue);

        const successContainer = new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('## Success'),
                new TextDisplayBuilder().setContent(`**status**: ✅ Input processed successfully`),
                new TextDisplayBuilder().setContent(`**result**: ${result}`)
            )
            .setAccentColor(LOG_COLORS.SUCCESS);

        return [successContainer];

    }, {
        autoDefer: false,
        ephemeral: true,
        fallbackMessage: '❌ Form processing failed.'
    });
}

// Input validation helper
function validateInput(input) {
    if (!input || input.trim().length === 0) {
        return {
            isValid: false,
            error: 'Input cannot be empty',
            suggestion: 'Please provide a valid value'
        };
    }

    if (input.length > 100) {
        return {
            isValid: false,
            error: 'Input too long',
            suggestion: 'Please keep input under 100 characters'
        };
    }

    return { isValid: true };
}
```

---

## 📚 **Common Implementation Patterns**

### **Pagination Pattern**
```javascript
// Paginated Content Display
function buildPaginatedContainer(items, currentPage, itemsPerPage) {
    const startIndex = currentPage * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageItems = items.slice(startIndex, endIndex);
    const totalPages = Math.ceil(items.length / itemsPerPage);

    const container = new ContainerBuilder();

    // Page header
    container.addTextDisplayComponents(
        new TextDisplayBuilder().setContent(`## Items (Page ${currentPage + 1}/${totalPages})`)
    );

    // Items display
    pageItems.forEach((item, index) => {
        container.addTextDisplayComponents(
            new TextDisplayBuilder().setContent(`**${startIndex + index + 1}.** ${item.name} - ${item.description}`)
        );
    });

    // Navigation buttons
    const buttons = [];

    if (currentPage > 0) {
        buttons.push(
            new ButtonBuilder()
                .setCustomId(`page-${currentPage - 1}`)
                .setLabel('Previous')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('⬅️')
        );
    }

    if (currentPage < totalPages - 1) {
        buttons.push(
            new ButtonBuilder()
                .setCustomId(`page-${currentPage + 1}`)
                .setLabel('Next')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('➡️')
        );
    }

    if (buttons.length > 0) {
        const buttonRow = new ActionRowBuilder().addComponents(buttons);
        container.addActionRowComponents(buttonRow);
    }

    container.setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}
```

### **Loading State Pattern**
```javascript
// Loading/Processing State
function buildLoadingContainer(operation) {
    return new ContainerBuilder()
        .addTextDisplayComponents(
            new TextDisplayBuilder().setContent(`**status**: 🔄 ${operation}...`),
            new TextDisplayBuilder().setContent('Please wait, this may take a moment.')
        )
        .setAccentColor(LOG_COLORS.INFO);
}

// Usage in background operations
async function handleLongOperation(interaction) {
    return handleUIOperation(interaction, async (interaction) => {
        // Return immediate loading state
        const loadingContainer = buildLoadingContainer('Processing request');

        // Start background operation
        processInBackground(interaction);

        return [loadingContainer];
    }, {
        autoDefer: true,
        ephemeral: true,
        fallbackMessage: '❌ Operation failed to start.'
    });
}

async function processInBackground(interaction) {
    try {
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Update with result
        const resultContainer = new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('**status**: ✅ Operation completed successfully')
            )
            .setAccentColor(LOG_COLORS.SUCCESS);

        await interaction.editReply({
            components: [resultContainer],
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral
        });

    } catch (error) {
        const errorContainer = new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('**status**: ❌ Operation failed')
            )
            .setAccentColor(OPERATION_COLORS.DELETE);

        await interaction.editReply({
            components: [errorContainer],
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral
        });
    }
}
```

---

## 🛠️ **Debugging & Testing Patterns**

### **Component Validation**
```javascript
// Validate components before returning
function validateComponents(components) {
    if (!Array.isArray(components)) {
        console.error('[validation] Components must be an array');
        return false;
    }

    for (const component of components) {
        if (!(component instanceof ContainerBuilder) &&
            !(component instanceof ActionRowBuilder)) {
            console.error('[validation] Invalid component type:', component.constructor.name);
            return false;
        }
    }

    return true;
}

// Usage in handlers
return handleUIOperation(interaction, async (interaction) => {
    const components = [container, buttonRow];

    if (!validateComponents(components)) {
        throw new Error('Invalid component structure');
    }

    return components;
}, config);
```

### **Development Helpers**
```javascript
// Debug container contents
function debugContainer(container, label = 'Container') {
    if (process.env.NODE_ENV === 'development') {
        console.log(`[debug] ${label}:`, {
            type: container.constructor.name,
            hasTextDisplays: container.textDisplayComponents?.length || 0,
            hasSections: container.sectionComponents?.length || 0,
            hasActionRows: container.actionRowComponents?.length || 0,
            accentColor: container.accentColor
        });
    }
}

// Syntax validation helper
function validateSyntax(filePath) {
    const { execSync } = require('child_process');
    try {
        execSync(`node --check ${filePath}`, { stdio: 'pipe' });
        console.log(`✅ ${filePath} syntax is valid`);
        return true;
    } catch (error) {
        console.error(`❌ ${filePath} syntax error:`, error.message);
        return false;
    }
}
```

---

**This comprehensive reference guide covers all Discord.js 14.21.0 patterns, architectural standards, and implementation examples used in the bot. Always refer to this guide when implementing new features to maintain consistency and prevent implementation errors.** 🎯✨
