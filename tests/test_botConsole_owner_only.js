/**
 * Comprehensive test script to verify botConsole specialty event log is bot owner only
 * Tests real Discord interactions by logging in as the bot and testing actual functionality
 */

require('dotenv').config();
const { Client, GatewayIntentBits, SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { mongoClient, connect } = require('../mongo/client.js');
const config = require('../config.js');

class BotConsoleOwnerTest {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.client = null;
        this.testGuild = null;
        this.testChannel = null;
    }

    /**
     * Initialize bot client and connect to Discord
     */
    async initializeBot() {
        console.log('🤖 Initializing bot client...');

        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent
            ]
        });

        return new Promise((resolve, reject) => {
            this.client.once('ready', async () => {
                console.log(`✅ Bot logged in as ${this.client.user.tag}`);

                // Get test guild and channel
                this.testGuild = this.client.guilds.cache.get(process.env.GUILDIDTWO);
                if (!this.testGuild) {
                    reject(new Error('Test guild not found'));
                    return;
                }

                this.testChannel = this.testGuild.channels.cache.find(ch => ch.isTextBased());
                if (!this.testChannel) {
                    reject(new Error('No text channel found in test guild'));
                    return;
                }

                console.log(`✅ Using guild: ${this.testGuild.name}`);
                console.log(`✅ Using channel: ${this.testChannel.name}`);
                resolve();
            });

            this.client.login(process.env.TOKEN).catch(reject);
        });
    }

    /**
     * Test configuration structure
     */
    testConfigStructure() {
        this.totalTests++;

        console.log('🧪 Testing config.js structure...');

        // Test 1: botConsole should be in ownerEvents
        if (config.ownerEvents.includes('botConsole')) {
            this.passedTests++;
            console.log('✅ botConsole is in ownerEvents array');
            this.testResults.push({ test: 'botConsole in ownerEvents', status: 'PASS' });
        } else {
            console.log('❌ botConsole is NOT in ownerEvents array');
            this.testResults.push({ test: 'botConsole in ownerEvents', status: 'FAIL' });
        }

        this.totalTests++;

        // Test 2: botConsole should NOT be in specialtyEvents
        if (!config.specialtyEvents.includes('botConsole')) {
            this.passedTests++;
            console.log('✅ botConsole is NOT in specialtyEvents array (correctly removed)');
            this.testResults.push({ test: 'botConsole removed from specialtyEvents', status: 'PASS' });
        } else {
            console.log('❌ botConsole is still in specialtyEvents array');
            this.testResults.push({ test: 'botConsole removed from specialtyEvents', status: 'FAIL' });
        }
    }

    /**
     * Test real logs command interaction as bot owner
     */
    async testOwnerLogsInteraction() {
        this.totalTests++;

        console.log('🧪 Testing real logs command interaction as bot owner...');

        try {
            // Import the logs command
            const logsCommand = require('../commands/utility/logs.js');

            // Create mock interaction as bot owner
            const ownerInteraction = {
                user: { id: process.env.OWNER },
                guild: this.testGuild,
                member: await this.testGuild.members.fetch(process.env.OWNER),
                reply: async (options) => {
                    console.log('   - Owner interaction reply called');
                    return { components: options.components || [] };
                },
                editReply: async (options) => {
                    console.log('   - Owner interaction editReply called');
                    return { components: options.components || [] };
                },
                deferReply: async () => {
                    console.log('   - Owner interaction deferred');
                }
            };

            // Execute logs command as owner
            await logsCommand.execute(ownerInteraction);

            this.passedTests++;
            console.log('✅ Owner can successfully execute logs command');
            this.testResults.push({ test: 'Owner logs command execution', status: 'PASS' });

        } catch (error) {
            console.log(`❌ Error testing owner logs interaction: ${error.message}`);
            this.testResults.push({ test: 'Owner logs command execution', status: 'ERROR', error: error.message });
        }
    }

    /**
     * Test database query for botConsole channels
     */
    async testBotConsoleChannelQuery() {
        this.totalTests++;

        console.log('🧪 Testing botConsole channel database query...');

        try {
            // Connect to database
            await connect();

            // Test the actual query used by consoleLogger
            const guildsCol = mongoClient.db("seventeen_bot").collection("guilds");
            const guilds = await guildsCol.find({
                "logs.enabled": true,
                "logs.channels": {
                    $elemMatch: {
                        "events": "botConsole"
                    }
                }
            }).toArray();

            console.log(`   - Found ${guilds.length} guilds with botConsole logging enabled`);

            // Test that the query structure is correct
            this.passedTests++;
            console.log('✅ botConsole database query structure is correct');
            this.testResults.push({ test: 'botConsole database query', status: 'PASS' });

        } catch (error) {
            console.log(`❌ Error testing botConsole database query: ${error.message}`);
            this.testResults.push({ test: 'botConsole database query', status: 'ERROR', error: error.message });
        }
    }

    /**
     * Test actual console logging functionality
     */
    async testConsoleLoggingFunctionality() {
        this.totalTests++;

        console.log('🧪 Testing actual console logging functionality...');

        try {
            const { logger } = require('../utils/consoleLogger.js');

            // Test that logger object has expected methods
            if (logger && typeof logger.info === 'function' && typeof logger.error === 'function') {
                // Test that we can call the logger methods
                logger.info('TEST', 'botConsole owner test message', this.client);

                this.passedTests++;
                console.log('✅ Console logger executed successfully');
                console.log('   - logger.info() method works');
                this.testResults.push({ test: 'Console logging functionality', status: 'PASS' });
            } else {
                console.log('❌ Console logger structure is incorrect');
                this.testResults.push({ test: 'Console logging functionality', status: 'FAIL' });
            }

        } catch (error) {
            console.log(`❌ Error testing console logging: ${error.message}`);
            this.testResults.push({ test: 'Console logging functionality', status: 'ERROR', error: error.message });
        }
    }

    /**
     * Test owner-only visibility in logs UI components
     */
    async testOwnerOnlyUIComponents() {
        this.totalTests++;

        console.log('🧪 Testing owner-only UI components...');

        try {
            // Import logs utility functions
            const { buildLogsContainer } = require('../commands/utility/logs.js');

            // Mock guild data with logs enabled
            const mockLogs = {
                enabled: true,
                channels: []
            };

            // Mock owner interaction
            const ownerInteraction = {
                user: { id: process.env.OWNER },
                guild: this.testGuild
            };

            // Mock non-owner interaction
            const nonOwnerInteraction = {
                user: { id: '123456789012345678' },
                guild: this.testGuild
            };

            // Test that owner events are only visible to owner
            const ownerEvents = config.ownerEvents;
            const ownerCanSeeOwnerEvents = ownerInteraction.user.id === process.env.OWNER;
            const nonOwnerCanSeeOwnerEvents = nonOwnerInteraction.user.id === process.env.OWNER;

            if (ownerCanSeeOwnerEvents && !nonOwnerCanSeeOwnerEvents && ownerEvents.includes('botConsole')) {
                this.passedTests++;
                console.log('✅ botConsole is properly restricted to owner-only UI components');
                this.testResults.push({ test: 'Owner-only UI components', status: 'PASS' });
            } else {
                console.log('❌ botConsole UI restriction failed');
                this.testResults.push({ test: 'Owner-only UI components', status: 'FAIL' });
            }

        } catch (error) {
            console.log(`❌ Error testing UI components: ${error.message}`);
            this.testResults.push({ test: 'Owner-only UI components', status: 'ERROR', error: error.message });
        }
    }

    /**
     * Cleanup bot connection
     */
    async cleanup() {
        if (this.client) {
            console.log('🧹 Cleaning up bot connection...');
            await this.client.destroy();
        }
    }

    /**
     * Run all comprehensive tests
     */
    async runAllTests() {
        console.log('🧪 Comprehensive botConsole Owner-Only Restriction Test');
        console.log('================================================================================');

        try {
            // Initialize bot connection
            await this.initializeBot();

            // Run configuration tests
            this.testConfigStructure();

            // Run real interaction tests
            await this.testOwnerLogsInteraction();
            await this.testBotConsoleChannelQuery();
            await this.testConsoleLoggingFunctionality();
            await this.testOwnerOnlyUIComponents();

        } catch (error) {
            console.log(`❌ Critical error during testing: ${error.message}`);
            this.testResults.push({ test: 'Test execution', status: 'ERROR', error: error.message });
        } finally {
            await this.cleanup();
        }

        console.log('\n📊 Test Results Summary');
        console.log('================================================================================');
        console.log(`Total Tests: ${this.totalTests}`);
        console.log(`Passed: ${this.passedTests}`);
        console.log(`Failed: ${this.totalTests - this.passedTests}`);
        console.log(`Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);

        if (this.passedTests === this.totalTests) {
            console.log('\n🎉 All botConsole owner restriction tests passed!');
            console.log('✅ botConsole is properly restricted to bot owner only');
        } else {
            console.log('\n⚠️  Some tests failed. botConsole may not be properly restricted.');

            // Show failed tests
            const failedTests = this.testResults.filter(r => r.status !== 'PASS');
            if (failedTests.length > 0) {
                console.log('\n❌ Failed Tests:');
                failedTests.forEach(test => {
                    console.log(`   - ${test.test}: ${test.error || 'Failed'}`);
                });
            }
        }

        return this.passedTests === this.totalTests;
    }
}

// Run the tests
async function main() {
    const tester = new BotConsoleOwnerTest();
    const success = await tester.runAllTests();
    
    if (success) {
        console.log('\n✅ All botConsole owner restriction tests passed!');
        process.exit(0);
    } else {
        console.log('\n❌ Some tests failed. Please review the results above.');
        process.exit(1);
    }
}

// Execute if run directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { BotConsoleOwnerTest };
