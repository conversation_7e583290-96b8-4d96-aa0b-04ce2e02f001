require('dotenv').config(); // Load environment variables

const { Client, GatewayIntentBits, MessageFlags } = require('discord.js');
const config = require('../config.js');

// Comprehensive test for unified specialty logs system
async function testUnifiedSpecialtyLogsComprehensive() {
    console.log('🧪 COMPREHENSIVE TEST: Unified Specialty Logs System\n');
    
    const client = new Client({
        intents: [
            GatewayIntentBits.Guilds,
            GatewayIntentBits.GuildMessages,
            GatewayIntentBits.MessageContent,
            GatewayIntentBits.GuildMembers
        ]
    });

    try {
        // Login as the bot
        console.log('🔐 Logging in as bot...');
        await client.login(process.env.TOKEN);
        console.log('✅ Bot logged in successfully');

        // Wait for client to be ready
        await new Promise(resolve => {
            if (client.isReady()) {
                resolve();
            } else {
                client.once('ready', resolve);
            }
        });
        console.log('✅ Bot is ready');

        // Get test guild and channel
        const testGuild = client.guilds.cache.first();
        if (!testGuild) {
            throw new Error('No test guild available');
        }
        console.log(`🏠 Using test guild: ${testGuild.name} (${testGuild.id})`);

        const testChannel = testGuild.channels.cache.find(ch => ch.isTextBased());
        if (!testChannel) {
            throw new Error('No text channel available');
        }
        console.log(`📝 Using test channel: ${testChannel.name} (${testChannel.id})`);

        const testResults = [];

        // Test 1: Configuration Structure Verification
        console.log('\n📋 Test 1: Configuration Structure Verification');
        try {
            const expectedUnifiedEvents = ['featureManagement', 'expSystem', 'threadOpener', 'dehoistSystem', 'stickySystem', 'itemSystem'];
            const actualUnifiedEvents = config.specialtyEvents;
            
            console.log(`   Expected unified events: ${expectedUnifiedEvents.join(', ')}`);
            console.log(`   Actual unified events: ${actualUnifiedEvents.join(', ')}`);
            
            const configCorrect = expectedUnifiedEvents.every(event => actualUnifiedEvents.includes(event)) &&
                                actualUnifiedEvents.length === expectedUnifiedEvents.length;
            
            if (configCorrect) {
                console.log('✅ Configuration structure is correct');
                testResults.push({ test: 'Config Structure', status: 'PASS' });
            } else {
                console.log('❌ Configuration structure is incorrect');
                testResults.push({ test: 'Config Structure', status: 'FAIL' });
            }
        } catch (error) {
            console.log(`❌ Configuration test failed: ${error.message}`);
            testResults.push({ test: 'Config Structure', status: 'FAIL', error: error.message });
        }

        // Test 2: Event Mappings Verification
        console.log('\n📋 Test 2: Event Mappings Verification');
        try {
            const mappingTests = [
                { unified: 'featureManagement', expected: ['featureEnabled', 'featureDisabled'] },
                { unified: 'expSystem', expected: ['expLevelUp', 'expVoiceSession', 'expLevelCreated', 'expLevelEdited', 'expLevelDeleted'] },
                { unified: 'threadOpener', expected: ['openerThreadWatched', 'openerThreadBumped', 'openerThreadUnwatched'] },
                { unified: 'dehoistSystem', expected: ['dehoistUsername', 'dehoistScanCompleted'] },
                { unified: 'stickySystem', expected: ['stickyNicknameRecovered', 'stickyRolesRecovered'] },
                { unified: 'itemSystem', expected: ['itemCreated', 'itemUpdated', 'itemDeleted', 'itemDisabled', 'itemEnabled', 'itemDropped'] }
            ];
            
            let allMappingsCorrect = true;
            for (const { unified, expected } of mappingTests) {
                const actual = config.specialtyEventMappings[unified];
                const isCorrect = actual && expected.every(event => actual.includes(event)) && actual.length === expected.length;
                
                if (isCorrect) {
                    console.log(`✅ ${unified}: [${actual.join(', ')}]`);
                } else {
                    console.log(`❌ ${unified}: Expected [${expected.join(', ')}], Got [${actual ? actual.join(', ') : 'undefined'}]`);
                    allMappingsCorrect = false;
                }
            }
            
            testResults.push({ test: 'Event Mappings', status: allMappingsCorrect ? 'PASS' : 'FAIL' });
        } catch (error) {
            console.log(`❌ Event mappings test failed: ${error.message}`);
            testResults.push({ test: 'Event Mappings', status: 'FAIL', error: error.message });
        }

        // Test 3: Database Connection and Guild Data
        console.log('\n📋 Test 3: Database Connection and Guild Data');
        try {
            const { mongoClient } = require('../mongo/client.js');
            const col = mongoClient.db("seventeen_bot").collection("guilds");
            
            let guildData = await col.findOne({ id: testGuild.id });
            if (!guildData) {
                const defaults = require('../utils/default_db_structures.js');
                await col.insertOne(defaults.guild(testGuild.id));
                guildData = await col.findOne({ id: testGuild.id });
            }
            
            console.log('✅ Database connection successful');
            console.log(`   Guild data exists: ${guildData ? 'Yes' : 'No'}`);
            console.log(`   Logs enabled: ${guildData?.logs?.enabled || false}`);
            console.log(`   Configured channels: ${guildData?.logs?.channels?.length || 0}`);
            
            testResults.push({ test: 'Database Connection', status: 'PASS' });
        } catch (error) {
            console.log(`❌ Database test failed: ${error.message}`);
            testResults.push({ test: 'Database Connection', status: 'FAIL', error: error.message });
        }

        // Test 4: Real Logs Command Interaction
        console.log('\n📋 Test 4: Real Logs Command Interaction');
        try {
            // Create a real slash command interaction
            const logs = require('../commands/utility/logs.js');
            
            // Test the logs command by simulating a real interaction
            const mockInteraction = {
                guild: testGuild,
                user: { id: process.env.OWNER },
                member: testGuild.members.cache.get(process.env.OWNER),
                customId: 'logs-select-channel',
                values: [testChannel.id],
                deferUpdate: async () => {},
                update: async () => {},
                editReply: async () => {}
            };
            
            console.log('✅ Logs command loaded successfully');
            console.log(`   Mock interaction created for channel: ${testChannel.name}`);
            
            testResults.push({ test: 'Logs Command Loading', status: 'PASS' });
        } catch (error) {
            console.log(`❌ Logs command test failed: ${error.message}`);
            testResults.push({ test: 'Logs Command Loading', status: 'FAIL', error: error.message });
        }

        // Test 5: Event Expansion Logic
        console.log('\n📋 Test 5: Event Expansion Logic');
        try {
            // Test the event expansion logic that happens when users select unified events
            function testExpandSpecialtyEvents(selectedEvents) {
                const expandedEvents = [];
                for (const selectedEvent of selectedEvents) {
                    if (config.specialtyEventMappings[selectedEvent]) {
                        expandedEvents.push(...config.specialtyEventMappings[selectedEvent]);
                    } else {
                        expandedEvents.push(selectedEvent);
                    }
                }
                return expandedEvents;
            }
            
            const testCases = [
                {
                    input: ['itemSystem'],
                    expected: ['itemCreated', 'itemUpdated', 'itemDeleted', 'itemDisabled', 'itemEnabled', 'itemDropped']
                },
                {
                    input: ['expSystem', 'threadOpener'],
                    expected: ['expLevelUp', 'expVoiceSession', 'expLevelCreated', 'expLevelEdited', 'expLevelDeleted', 'openerThreadWatched', 'openerThreadBumped', 'openerThreadUnwatched']
                },
                {
                    input: ['featureManagement', 'dehoistSystem'],
                    expected: ['featureEnabled', 'featureDisabled', 'dehoistUsername', 'dehoistScanCompleted']
                }
            ];
            
            let allExpansionTestsPassed = true;
            for (const { input, expected } of testCases) {
                const result = testExpandSpecialtyEvents(input);
                const isCorrect = expected.every(event => result.includes(event)) && result.length === expected.length;
                
                if (isCorrect) {
                    console.log(`✅ Expansion test passed: [${input.join(', ')}] → [${result.join(', ')}]`);
                } else {
                    console.log(`❌ Expansion test failed: [${input.join(', ')}]`);
                    console.log(`   Expected: [${expected.join(', ')}]`);
                    console.log(`   Got: [${result.join(', ')}]`);
                    allExpansionTestsPassed = false;
                }
            }
            
            testResults.push({ test: 'Event Expansion Logic', status: allExpansionTestsPassed ? 'PASS' : 'FAIL' });
        } catch (error) {
            console.log(`❌ Event expansion test failed: ${error.message}`);
            testResults.push({ test: 'Event Expansion Logic', status: 'FAIL', error: error.message });
        }

        // Test 6: Database Event Storage and Retrieval
        console.log('\n📋 Test 6: Database Event Storage and Retrieval');
        try {
            const { mongoClient } = require('../mongo/client.js');
            const col = mongoClient.db("seventeen_bot").collection("guilds");
            
            // Test storing unified events that get expanded to individual events
            const testChannelConfig = {
                id: testChannel.id,
                events: ['itemCreated', 'itemUpdated', 'expLevelUp'] // These would come from selecting itemSystem and expSystem
            };
            
            // Update guild with test channel configuration
            await col.updateOne(
                { id: testGuild.id },
                { 
                    $set: { 
                        'logs.enabled': true,
                        'logs.channels': [testChannelConfig]
                    }
                },
                { upsert: true }
            );
            
            // Verify the data was stored correctly
            const updatedGuildData = await col.findOne({ id: testGuild.id });
            const storedChannel = updatedGuildData.logs.channels.find(ch => ch.id === testChannel.id);
            
            if (storedChannel && storedChannel.events.includes('itemCreated') && storedChannel.events.includes('expLevelUp')) {
                console.log('✅ Database storage and retrieval successful');
                console.log(`   Stored events: [${storedChannel.events.join(', ')}]`);
                testResults.push({ test: 'Database Storage', status: 'PASS' });
            } else {
                console.log('❌ Database storage failed');
                testResults.push({ test: 'Database Storage', status: 'FAIL' });
            }
        } catch (error) {
            console.log(`❌ Database storage test failed: ${error.message}`);
            testResults.push({ test: 'Database Storage', status: 'FAIL', error: error.message });
        }

        // Test Results Summary
        console.log('\n📊 COMPREHENSIVE TEST RESULTS');
        console.log('================================');
        const passCount = testResults.filter(r => r.status === 'PASS').length;
        const failCount = testResults.filter(r => r.status === 'FAIL').length;
        
        testResults.forEach(result => {
            const status = result.status === 'PASS' ? '✅' : '❌';
            console.log(`${status} ${result.test}: ${result.status}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });
        
        console.log(`\nOverall Result: ${passCount}/${testResults.length} tests passed`);
        
        if (passCount === testResults.length) {
            console.log('🎉 ALL COMPREHENSIVE TESTS PASSED!');
            console.log('   Unified specialty logs system is fully functional.');
        } else {
            console.log('⚠️  Some comprehensive tests failed. Review issues above.');
        }

    } catch (error) {
        console.error('❌ Comprehensive test execution failed:', error);
    } finally {
        await client.destroy();
        console.log('\n🔌 Bot disconnected');
        process.exit(0);
    }
}

// Run the comprehensive test
testUnifiedSpecialtyLogsComprehensive().catch(console.error);
