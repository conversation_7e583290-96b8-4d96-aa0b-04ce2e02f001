#!/bin/bash

echo "🎤 Setting up Transcription Feature..."
echo "======================================"

# Check if Python is available
if command -v python3 &> /dev/null; then
    echo "✅ Python 3 found: $(python3 --version)"
else
    echo "❌ Python 3 not found. Please install Python 3.8+ first."
    exit 1
fi

# Check if pip is available
if command -v pip3 &> /dev/null; then
    echo "✅ pip3 found"
elif command -v pip &> /dev/null; then
    echo "✅ pip found"
else
    echo "❌ pip not found. Please install pip first."
    exit 1
fi

# Install Python dependencies
echo ""
echo "📦 Installing Python dependencies..."
pip3 install openai-whisper || pip install openai-whisper

if [ $? -eq 0 ]; then
    echo "✅ OpenAI Whisper installed successfully"
else
    echo "❌ Failed to install OpenAI Whisper"
    exit 1
fi

# Test Whisper installation
echo ""
echo "🧪 Testing Whisper installation..."
python3 -c "import whisper; print('✅ Whisper module available')" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Whisper installation verified"
else
    echo "❌ Whisper installation failed"
    exit 1
fi

# Create required directories
echo ""
echo "📁 Creating required directories..."
mkdir -p temp_audio
mkdir -p transcripts
echo "✅ Directories created"

# Test FFmpeg (bundled with Node.js)
echo ""
echo "🎬 Testing bundled FFmpeg..."
node -e "
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const { spawn } = require('child_process');
const ffmpeg = spawn(ffmpegPath, ['-version'], { stdio: 'pipe' });
ffmpeg.on('close', (code) => {
    if (code === 0) {
        console.log('✅ Bundled FFmpeg working correctly');
    } else {
        console.log('❌ Bundled FFmpeg test failed');
        process.exit(1);
    }
});
ffmpeg.on('error', (error) => {
    console.log('❌ FFmpeg error:', error.message);
    process.exit(1);
});
"

echo ""
echo "🎉 Transcription setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Start your bot: node index.js"
echo "2. Use /owner command to pre-download Whisper models"
echo "3. Test transcription with /you → transcribe"
echo ""
echo "🔧 Troubleshooting:"
echo "- If transcription fails, check console for errors"
echo "- Ensure your .env file has all required variables"
echo "- Use /owner → whisper models to download models in advance"
